// Enhanced dealership schema with mode configuration
// Add to shared/schema.ts

export const dealershipModes = ['rylie_ai', 'direct_agent'] as const;
export type DealershipMode = typeof dealershipModes[number];

export const communicationChannels = ['chat', 'email', 'sms', 'phone'] as const;
export type CommunicationChannel = typeof communicationChannels[number];

// Enhanced dealerships table (add these columns to existing table)
export const dealershipEnhancements = {
  // Core mode setting
  operation_mode: varchar('operation_mode', { length: 50 }).$type<DealershipMode>().default('rylie_ai'),
  
  // AI Configuration (for Rylie AI mode)
  ai_config: json('ai_config').$type<{
    purecars_api_key?: string;
    purecars_endpoint?: string;
    ai_personality?: string;
    response_delay_ms?: number;
    escalation_triggers?: string[];
  }>().default({}),
  
  // Direct Agent Configuration (for Direct Agent mode)
  agent_config: json('agent_config').$type<{
    enabled_channels: CommunicationChannel[];
    auto_assignment: boolean;
    working_hours: {
      timezone: string;
      schedule: Record<string, { start: string; end: string; enabled: boolean }>;
    };
    escalation_rules: {
      response_time_minutes: number;
      max_queue_size: number;
      priority_routing: boolean;
    };
    templates: {
      greeting_message?: string;
      away_message?: string;
      queue_message?: string;
    };
  }>().default({
    enabled_channels: ['chat', 'email'],
    auto_assignment: false,
    working_hours: {
      timezone: 'America/New_York',
      schedule: {
        monday: { start: '09:00', end: '17:00', enabled: true },
        tuesday: { start: '09:00', end: '17:00', enabled: true },
        wednesday: { start: '09:00', end: '17:00', enabled: true },
        thursday: { start: '09:00', end: '17:00', enabled: true },
        friday: { start: '09:00', end: '17:00', enabled: true },
        saturday: { start: '10:00', end: '16:00', enabled: true },
        sunday: { start: '12:00', end: '16:00', enabled: false }
      }
    },
    escalation_rules: {
      response_time_minutes: 5,
      max_queue_size: 10,
      priority_routing: true
    },
    templates: {}
  }),
  
  // Lead routing configuration
  lead_routing: json('lead_routing').$type<{
    auto_create_leads: boolean;
    default_lead_source: string;
    lead_assignment_strategy: 'round_robin' | 'least_busy' | 'skill_based' | 'manual';
    scoring_enabled: boolean;
    follow_up_automation: boolean;
  }>().default({
    auto_create_leads: true,
    default_lead_source: 'website_chat',
    lead_assignment_strategy: 'round_robin',
    scoring_enabled: true,
    follow_up_automation: true
  })
};

// Configuration service
export class DealershipConfigService {
  
  async getDealershipMode(dealershipId: number): Promise<DealershipMode> {
    try {
      const result = await db.execute(sql`
        SELECT operation_mode FROM dealerships WHERE id = ${dealershipId}
      `);
      
      return (result.rows[0] as any)?.operation_mode || 'rylie_ai';
    } catch (error) {
      logger.error('Error getting dealership mode', { error, dealershipId });
      return 'rylie_ai'; // Default fallback
    }
  }

  async updateDealershipMode(
    dealershipId: number, 
    mode: DealershipMode,
    config?: any
  ): Promise<void> {
    try {
      let updateQuery = sql`
        UPDATE dealerships 
        SET operation_mode = ${mode}, updated_at = ${new Date().toISOString()}
      `;

      if (mode === 'rylie_ai' && config?.ai_config) {
        updateQuery = sql`
          UPDATE dealerships 
          SET operation_mode = ${mode}, 
              ai_config = ${JSON.stringify(config.ai_config)},
              updated_at = ${new Date().toISOString()}
        `;
      } else if (mode === 'direct_agent' && config?.agent_config) {
        updateQuery = sql`
          UPDATE dealerships 
          SET operation_mode = ${mode}, 
              agent_config = ${JSON.stringify(config.agent_config)},
              updated_at = ${new Date().toISOString()}
        `;
      }

      updateQuery = sql`${updateQuery} WHERE id = ${dealershipId}`;
      
      await db.execute(updateQuery);
      
      // Invalidate cache
      await cacheService.invalidatePattern(`dealership:${dealershipId}`);
      
      logger.info('Dealership mode updated', { dealershipId, mode });
    } catch (error) {
      logger.error('Error updating dealership mode', { error, dealershipId, mode });
      throw error;
    }
  }

  async getDealershipConfig(dealershipId: number): Promise<any> {
    try {
      const cacheKey = `dealership:${dealershipId}:config`;
      
      return await cacheService.getOrSet(cacheKey, async () => {
        const result = await db.execute(sql`
          SELECT 
            operation_mode,
            ai_config,
            agent_config,
            lead_routing
          FROM dealerships 
          WHERE id = ${dealershipId}
        `);

        if (result.rows.length === 0) {
          throw new Error(`Dealership not found: ${dealershipId}`);
        }

        const row = result.rows[0] as any;
        return {
          mode: row.operation_mode,
          aiConfig: JSON.parse(row.ai_config || '{}'),
          agentConfig: JSON.parse(row.agent_config || '{}'),
          leadRouting: JSON.parse(row.lead_routing || '{}')
        };
      }, { ttl: 300 }); // Cache for 5 minutes

    } catch (error) {
      logger.error('Error getting dealership config', { error, dealershipId });
      throw error;
    }
  }

  async isRylieMode(dealershipId: number): Promise<boolean> {
    const mode = await this.getDealershipMode(dealershipId);
    return mode === 'rylie_ai';
  }

  async isDirectAgentMode(dealershipId: number): Promise<boolean> {
    const mode = await this.getDealershipMode(dealershipId);
    return mode === 'direct_agent';
  }

  async getAgentWorkingHours(dealershipId: number): Promise<any> {
    try {
      const config = await this.getDealershipConfig(dealershipId);
      return config.agentConfig.working_hours || {};
    } catch (error) {
      logger.error('Error getting working hours', { error, dealershipId });
      return {};
    }
  }

  async isWithinWorkingHours(dealershipId: number): Promise<boolean> {
    try {
      const workingHours = await this.getAgentWorkingHours(dealershipId);
      const now = new Date();
      const dayOfWeek = now.toLocaleDateString('en-US', { weekday: 'lowercase' });
      
      const todaySchedule = workingHours.schedule?.[dayOfWeek];
      if (!todaySchedule || !todaySchedule.enabled) {
        return false;
      }

      const currentTime = now.toTimeString().substr(0, 5); // HH:MM format
      return currentTime >= todaySchedule.start && currentTime <= todaySchedule.end;
    } catch (error) {
      logger.error('Error checking working hours', { error, dealershipId });
      return true; // Default to available
    }
  }
}

export default DealershipConfigService;