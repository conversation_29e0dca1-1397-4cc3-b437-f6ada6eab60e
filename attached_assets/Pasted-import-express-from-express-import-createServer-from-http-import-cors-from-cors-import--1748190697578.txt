import express from 'express';
import { createServer } from 'http';
import cors from 'cors';
import path from 'path';
import logger from './utils/logger';
import { checkDatabaseHealth } from './db';
import { cacheService } from './services/redis-cache';
import ChatServer from './services/chat-server';
import ConversationRouter from './services/conversation-router';
import MessagingService from './services/messaging-service';

// Import route handlers
import setupRoutes from './routes/setup-routes';
import performanceRoutes from './routes/performance-routes';
import monitoringRoutes from './routes/monitoring-routes';
// Add your existing routes here
// import authRoutes from './routes/auth-routes';
// import leadRoutes from './routes/lead-routes';

const app = express();
const server = createServer(app);
const PORT = process.env.PORT || 5000;

// Initialize services
const chatServer = new ChatServer();
const messagingService = new MessagingService();
const conversationRouter = new ConversationRouter(chatServer);

// Middleware
app.use(cors({
  origin: process.env.CLIENT_URL || 'http://localhost:5000',
  credentials: true
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Request logging middleware
app.use((req, res, next) => {
  logger.info(`${req.method} ${req.path}`, {
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });
  next();
});

// Health check endpoint
app.get('/api/health', async (req, res) => {
  try {
    const dbHealth = await checkDatabaseHealth();
    const cacheStats = await cacheService.getStats();
    
    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        database: dbHealth,
        cache: cacheStats.connected,
        websocket: true
      },
      uptime: process.uptime()
    });
  } catch (error) {
    logger.error('Health check failed', { error });
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Service unavailable'
    });
  }
});

// API Routes
app.use('/api/setup', setupRoutes);
app.use('/api/performance', performanceRoutes);
app.use('/api/monitoring', monitoringRoutes);

// Add your existing API routes here
// app.use('/api/auth', authRoutes);
// app.use('/api/leads', leadRoutes);
// app.use('/api/conversations', conversationRoutes);

// Conversation handling endpoint for external integrations
app.post('/api/conversations/message', async (req, res) => {
  try {
    const messageData = req.body;
    
    // Validate required fields
    if (!messageData.dealershipId || !messageData.content) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: dealershipId, content'
      });
    }

    // Route the message through our conversation router
    const response = await conversationRouter.routeIncomingMessage({
      dealershipId: messageData.dealershipId,
      customerName: messageData.customerName,
      customerEmail: messageData.customerEmail,
      customerPhone: messageData.customerPhone,
      content: messageData.content,
      channel: messageData.channel || 'chat',
      conversationId: messageData.conversationId,
      source: messageData.source || 'api',
      metadata: messageData.metadata
    });

    res.json(response);
  } catch (error) {
    logger.error('Error handling conversation message', { error });
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

// Serve static files in production
if (process.env.NODE_ENV === 'production') {
  app.use(express.static(path.join(__dirname, '../client/dist')));
  
  app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, '../client/dist/index.html'));
  });
} else {
  // Development mode - serve from Vite dev server
  app.get('/', (req, res) => {
    res.json({
      message: 'Rylie AI API Server',
      version: '1.0.0',
      mode: process.env.NODE_ENV || 'development',
      docs: '/api/health'
    });
  });
}

// Initialize WebSocket server
chatServer.initialize(server);

// Scheduled job for processing messages (run every minute)
setInterval(async () => {
  try {
    await messagingService.processScheduledMessages();
  } catch (error) {
    logger.error('Error processing scheduled messages', { error });
  }
}, 60000);

// Error handling middleware
app.use((error: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  logger.error('Unhandled error', {
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method
  });

  res.status(500).json({
    success: false,
    error: process.env.NODE_ENV === 'production' 
      ? 'Internal server error' 
      : error.message
  });
});

// 404 handler for API routes
app.use('/api/*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'API endpoint not found'
  });
});

// Graceful shutdown handling
process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);

async function gracefulShutdown(signal: string) {
  logger.info(`Received ${signal}. Starting graceful shutdown...`);
  
  try {
    // Close HTTP server
    server.close(() => {
      logger.info('HTTP server closed');
    });

    // Shutdown WebSocket server
    chatServer.shutdown();

    // Shutdown cache service
    await cacheService.shutdown();

    // Close database connections
    // await closeDatabaseConnection(); // Uncomment if you have this function

    logger.info('Graceful shutdown completed');
    process.exit(0);
  } catch (error) {
    logger.error('Error during graceful shutdown', { error });
    process.exit(1);
  }
}

// Start server
async function startServer() {
  try {
    // Check database connectivity
    const dbHealthy = await checkDatabaseHealth();
    if (!dbHealthy) {
      throw new Error('Database connection failed');
    }

    // Start HTTP server
    server.listen(PORT, () => {
      logger.info(`🚀 Rylie AI Server started`, {
        port: PORT,
        mode: process.env.NODE_ENV || 'development',
        timestamp: new Date().toISOString()
      });

      console.log(`
╔═══════════════════════════════════════════════════════════════╗
║                    🤖 RYLIE AI SERVER                         ║
║                                                               ║
║  Server running on: http://localhost:${PORT}                       ║
║  Health check:      http://localhost:${PORT}/api/health             ║
║  WebSocket:         ws://localhost:${PORT}/ws/chat                  ║
║                                                               ║
║  Features:                                                    ║
║  ✓ Dual-mode operation (AI + Direct Agent)                   ║
║  ✓ Real-time chat with WebSocket                             ║
║  ✓ Multi-channel messaging (Email, SMS, Chat)                ║
║  ✓ Lead management and scoring                               ║
║  ✓ Performance optimization with caching                     ║
║  ✓ Comprehensive monitoring                                  ║
║                                                               ║
╚═══════════════════════════════════════════════════════════════╝
      `);
    });

  } catch (error) {
    logger.error('Failed to start server', { error });
    process.exit(1);
  }
}

// Initialize and start the server
startServer();

export default app;