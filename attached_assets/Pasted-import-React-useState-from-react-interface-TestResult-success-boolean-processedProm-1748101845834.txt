import React, { useState } from 'react';

interface TestResult {
  success: boolean;
  processedPrompt: string;
  aiResponse: string;
  timestamp: string;
}

interface PromptTest {
  id: number;
  original_prompt: string;
  processed_prompt: string;
  ai_response: string;
  variables: Record<string, any>;
  created_at: string;
}

const PromptTestingPage: React.FC = () => {
  const [prompt, setPrompt] = useState('You are an automotive sales agent named {{Agent_Name}}, working for {{Dealership}}. Rewrite if off: Trim wordiness, boost empathy, fix compliance, or adjust tone to sound like a real salesperson (e.g., replace "We strive to assist" with "We\'ve got you covered!").');
  const [variables, setVariables] = useState<Record<string, string>>({
    Agent_Name: 'Sarah',
    Dealership: 'Premium Auto Sales'
  });
  const [newVariableKey, setNewVariableKey] = useState('');
  const [newVariableValue, setNewVariableValue] = useState('');
  const [testResult, setTestResult] = useState<TestResult | null>(null);
  const [showHistory, setShowHistory] = useState(false);
  const [testHistory, setTestHistory] = useState<PromptTest[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Extract variables from prompt template
  const extractVariables = (text: string): string[] => {
    const matches = text.match(/\{\{[^}]+\}\}/g) || [];
    return [...new Set(matches.map(match => match.replace(/[{}]/g, '').trim()))];
  };

  const promptVariables = extractVariables(prompt);

  // API call function
  const testPrompt = async (promptData: { prompt: string; variables?: Record<string, string> }) => {
    const response = await fetch('/api/prompt-test', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify(promptData),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  };

  const fetchTestHistory = async () => {
    const response = await fetch('/api/prompt-tests', {
      method: 'GET',
      credentials: 'include',
    });

    if (!response.ok) {
      throw new Error('Failed to fetch test history');
    }

    const data = await response.json();
    return data.tests || [];
  };

  const handleTest = async () => {
    if (!prompt.trim()) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await testPrompt({
        prompt: prompt.trim(),
        variables: variables,
      });
      setTestResult(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to test prompt');
    } finally {
      setIsLoading(false);
    }
  };

  const loadHistory = async () => {
    try {
      const history = await fetchTestHistory();
      setTestHistory(history);
      setShowHistory(true);
    } catch (err) {
      setError('Failed to load test history');
    }
  };

  const addVariable = () => {
    if (newVariableKey && newVariableValue) {
      setVariables(prev => ({
        ...prev,
        [newVariableKey]: newVariableValue
      }));
      setNewVariableKey('');
      setNewVariableValue('');
    }
  };

  const removeVariable = (key: string) => {
    const { [key]: removed, ...rest } = variables;
    setVariables(rest);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const processedTemplate = () => {
    let processed = prompt;
    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`\\{\\{\\s*${key}\\s*\\}\\}`, 'g');
      processed = processed.replace(regex, value);
    });
    return processed;
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Quick Test</h1>
            <p className="text-gray-600 mt-1">Test AI prompts with custom variables and scenarios</p>
          </div>
          <button
            onClick={showHistory ? () => setShowHistory(false) : loadHistory}
            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <span className="flex items-center gap-2">
              📊 {showHistory ? 'Hide' : 'Show'} History
            </span>
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Input Section */}
          <div className="space-y-6">
            {/* Prompt Template */}
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900 flex items-center gap-2">
                  ⚡ System Prompt Template
                </h3>
                <p className="text-sm text-gray-600 mt-1">
                  Edit the system prompt template. Use double curly braces like {`{{variable_name}}`} for personalization.
                </p>
              </div>
              <div className="px-6 py-4">
                <textarea
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  className="w-full min-h-[150px] px-3 py-2 border border-gray-300 rounded-md text-sm font-mono focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter your prompt template here..."
                />
                <div className="mt-3 flex flex-wrap gap-2">
                  {promptVariables.map(variable => (
                    <span
                      key={variable}
                      className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800"
                    >
                      {variable}
                    </span>
                  ))}
                </div>
              </div>
            </div>

            {/* Variables */}
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Template Variables</h3>
                <p className="text-sm text-gray-600 mt-1">
                  Set values for variables used in your prompt template
                </p>
              </div>
              <div className="px-6 py-4 space-y-4">
                {/* Existing Variables */}
                <div className="space-y-3">
                  {Object.entries(variables).map(([key, value]) => (
                    <div key={key} className="flex gap-2">
                      <input
                        type="text"
                        value={key}
                        readOnly
                        className="w-32 px-3 py-2 border border-gray-300 rounded-md text-sm bg-gray-50"
                      />
                      <input
                        type="text"
                        value={value}
                        onChange={(e) => setVariables(prev => ({
                          ...prev,
                          [key]: e.target.value
                        }))}
                        placeholder="Variable value"
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                      <button
                        onClick={() => removeVariable(key)}
                        className="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        Remove
                      </button>
                    </div>
                  ))}
                </div>

                {/* Add New Variable */}
                <div className="border-t pt-3">
                  <div className="flex gap-2">
                    <input
                      type="text"
                      value={newVariableKey}
                      onChange={(e) => setNewVariableKey(e.target.value)}
                      placeholder="Variable name"
                      className="w-32 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                    <input
                      type="text"
                      value={newVariableValue}
                      onChange={(e) => setNewVariableValue(e.target.value)}
                      placeholder="Variable value"
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                    <button
                      onClick={addVariable}
                      className="px-3 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      Add
                    </button>
                  </div>
                </div>

                {/* Test Button */}
                <button
                  onClick={handleTest}
                  disabled={isLoading || !prompt.trim()}
                  className="w-full px-4 py-3 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                >
                  {isLoading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      Testing...
                    </>
                  ) : (
                    <>
                      🚀 Test Prompt
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>

          {/* Output Section */}
          <div className="space-y-6">
            {/* Processed Template Preview */}
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Processed Template Preview</h3>
                <p className="text-sm text-gray-600 mt-1">
                  This is how the template looks with all variables replaced
                </p>
              </div>
              <div className="px-6 py-4">
                <div className="bg-gray-50 p-4 rounded-lg border font-mono text-sm whitespace-pre-wrap">
                  {processedTemplate()}
                </div>
                <button
                  onClick={() => copyToClipboard(processedTemplate())}
                  className="mt-3 px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  📋 Copy
                </button>
              </div>
            </div>

            {/* Test Results */}
            {testResult && (
              <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">AI Response</h3>
                  <p className="text-sm text-gray-600 mt-1">
                    Generated at {new Date(testResult.timestamp).toLocaleString()}
                  </p>
                </div>
                <div className="px-6 py-4">
                  <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                    <p className="text-gray-900 leading-relaxed">
                      {testResult.aiResponse}
                    </p>
                  </div>
                  <button
                    onClick={() => copyToClipboard(testResult.aiResponse)}
                    className="mt-3 px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    📋 Copy Response
                  </button>
                </div>
              </div>
            )}

            {/* Error Display */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <span className="text-red-400">⚠️</span>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-red-800">{error}</p>
                  </div>
                  <div className="ml-auto pl-3">
                    <button
                      onClick={() => setError(null)}
                      className="text-red-400 hover:text-red-600"
                    >
                      ✕
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* History Section */}
        {showHistory && (
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Test History</h3>
              <p className="text-sm text-gray-600 mt-1">
                Your recent prompt tests and results
              </p>
            </div>
            <div className="px-6 py-4">
              {testHistory.length ? (
                <div className="space-y-4">
                  {testHistory.slice(0, 10).map((test: PromptTest) => (
                    <div key={test.id} className="border rounded-lg p-4 space-y-3">
                      <div className="flex justify-between items-start">
                        <span className="text-sm text-gray-500">
                          {new Date(test.created_at).toLocaleString()}
                        </span>
                        <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800">
                          Test #{test.id}
                        </span>
                      </div>
                      <div className="text-sm">
                        <strong>Prompt:</strong> {test.original_prompt.substring(0, 100)}...
                      </div>
                      <div className="text-sm bg-blue-50 p-2 rounded border border-blue-200">
                        <strong>Response:</strong> {test.ai_response}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-8">
                  No test history yet. Run your first test to see results here.
                </p>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PromptTestingPage;