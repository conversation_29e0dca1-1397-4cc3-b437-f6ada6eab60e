import express, { Request, Response } from 'express';
import { db } from '../db';
import { sql } from 'drizzle-orm';
import { ResponseHelper, asyncHandler } from '../utils/errors';
import DealershipConfigService from '../services/dealership-config';
import logger from '../utils/logger';

const router = express.Router();
const configService = new DealershipConfigService();

// Get current dealership setup status
router.get('/status/:dealershipId', asyncHandler(async (req: Request, res: Response) => {
  const dealershipId = parseInt(req.params.dealershipId);

  try {
    const result = await db.execute(sql`
      SELECT 
        operation_mode,
        ai_config,
        agent_config,
        lead_routing,
        created_at
      FROM dealerships 
      WHERE id = ${dealershipId}
    `);

    if (result.rows.length === 0) {
      return ResponseHelper.notFound(res, 'Dealership');
    }

    const dealership = result.rows[0] as any;
    const isSetupComplete = dealership.operation_mode !== null;

    ResponseHelper.success(res, {
      isSetupComplete,
      mode: dealership.operation_mode,
      config: {
        ai: JSON.parse(dealership.ai_config || '{}'),
        agent: JSON.parse(dealership.agent_config || '{}'),
        leadRouting: JSON.parse(dealership.lead_routing || '{}')
      },
      setupDate: dealership.created_at
    });
  } catch (error) {
    const err = error instanceof Error ? error : new Error(String(error));
    logger.error('Error getting setup status', { error: err.message, dealershipId });
    ResponseHelper.error(res, err);
  }
}));

// Complete initial setup
router.post('/complete', asyncHandler(async (req: Request, res: Response) => {
  const {
    dealershipId,
    mode,
    dealershipInfo,
    rylieConfig,
    agentConfig,
    leadConfig
  } = req.body;

  if (!dealershipId || !mode) {
    return ResponseHelper.badRequest(res, 'Missing required fields: dealershipId, mode');
  }

  if (!['rylie_ai', 'direct_agent'].includes(mode)) {
    return ResponseHelper.badRequest(res, 'Invalid mode. Must be rylie_ai or direct_agent');
  }

  try {
    // Start transaction
    await db.execute(sql`BEGIN`);

    // Update dealership with setup configuration
    const updateQuery = sql`
      UPDATE dealerships SET
        operation_mode = ${mode},
        name = ${dealershipInfo.name},
        contact_email = ${dealershipInfo.email},
        contact_phone = ${dealershipInfo.phone || ''},
        website = ${dealershipInfo.website || ''},
        ai_config = ${JSON.stringify(mode === 'rylie_ai' ? rylieConfig : {})},
        agent_config = ${JSON.stringify(mode === 'direct_agent' ? agentConfig : {})},
        lead_routing = ${JSON.stringify(leadConfig)},
        updated_at = ${new Date().toISOString()}
      WHERE id = ${dealershipId}
    `;

    await db.execute(updateQuery);

    // Create default message templates if in direct agent mode
    if (mode === 'direct_agent') {
      await createDefaultTemplates(dealershipId, agentConfig);
    }

    // Create default lead sources
    await createDefaultLeadSources(dealershipId, mode);

    // Create default follow-up rules if automation is enabled
    if (leadConfig.followUpAutomation) {
      await createDefaultFollowUpRules(dealershipId);
    }

    // Commit transaction
    await db.execute(sql`COMMIT`);

    logger.info('Dealership setup completed', {
      dealershipId,
      mode,
      name: dealershipInfo.name
    });

    ResponseHelper.success(res, {
      message: 'Setup completed successfully',
      dealershipId,
      mode
    });

  } catch (error) {
    // Rollback transaction
    await db.execute(sql`ROLLBACK`);
    
    const err = error instanceof Error ? error : new Error(String(error));
    logger.error('Error completing setup', { error: err.message, dealershipId });
    ResponseHelper.error(res, err);
  }
}));

// Update operation mode
router.put('/mode/:dealershipId', asyncHandler(async (req: Request, res: Response) => {
  const dealershipId = parseInt(req.params.dealershipId);
  const { mode, config } = req.body;

  if (!['rylie_ai', 'direct_agent'].includes(mode)) {
    return ResponseHelper.badRequest(res, 'Invalid mode');
  }

  try {
    await configService.updateDealershipMode(dealershipId, mode, config);
    
    ResponseHelper.success(res, {
      message: 'Operation mode updated successfully',
      mode
    });
  } catch (error) {
    const err = error instanceof Error ? error : new Error(String(error));
    logger.error('Error