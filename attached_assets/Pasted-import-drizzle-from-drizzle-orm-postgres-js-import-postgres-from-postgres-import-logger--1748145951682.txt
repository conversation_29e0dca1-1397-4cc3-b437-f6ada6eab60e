import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { logger } from './logger';
import * as schema from '../shared/schema';

// Database configuration with improved connection management
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432'),
  database: process.env.DB_NAME || 'rylie_db',
  username: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'password',
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  
  // Connection pool configuration
  max: parseInt(process.env.DB_POOL_MAX || '20'),
  idle_timeout: parseInt(process.env.DB_IDLE_TIMEOUT || '30'),
  connect_timeout: parseInt(process.env.DB_CONNECT_TIMEOUT || '10'),
  
  // Retry configuration
  max_lifetime: 60 * 30, // 30 minutes
  transform: {
    undefined: null
  },
  
  // Connection management
  onnotice: (notice: any) => {
    logger.debug('PostgreSQL notice:', notice);
  },
  
  debug: process.env.NODE_ENV === 'development' ? 
    (connection: any, query: any, parameters: any) => {
      logger.debug('Database query:', { query, parameters });
    } : false
};

// Create connection with error handling
let sql: postgres.Sql<{}>;
let connectionAttempts = 0;
const maxRetries = 5;

async function createConnection(): Promise<postgres.Sql<{}>> {
  try {
    connectionAttempts++;
    
    // Create postgres connection
    sql = postgres(dbConfig);
    
    // Test the connection
    await sql`SELECT 1 as test`;
    
    logger.info('Database connection established successfully', {
      host: dbConfig.host,
      database: dbConfig.database,
      attempt: connectionAttempts
    });
    
    connectionAttempts = 0; // Reset on successful connection
    return sql;
    
  } catch (error) {
    logger.error('Database connection failed', {
      error: error.message,
      attempt: connectionAttempts,
      config: {
        host: dbConfig.host,
        database: dbConfig.database,
        port: dbConfig.port
      }
    });
    
    if (connectionAttempts < maxRetries) {
      logger.info(`Retrying database connection in ${connectionAttempts * 2} seconds...`);
      await new Promise(resolve => setTimeout(resolve, connectionAttempts * 2000));
      return createConnection();
    }
    
    throw new Error(`Failed to connect to database after ${maxRetries} attempts: ${error.message}`);
  }
}

// Initialize connection
const initializeDatabase = async () => {
  if (!sql) {
    sql = await createConnection();
  }
  return sql;
};

// Create drizzle instance with error handling
export const db = drizzle(await initializeDatabase(), { 
  schema,
  logger: process.env.NODE_ENV === 'development'
});

// Connection health check
export async function checkDatabaseHealth(): Promise<boolean> {
  try {
    await sql`SELECT 1 as health_check`;
    return true;
  } catch (error) {
    logger.error('Database health check failed:', error);
    return false;
  }
}

// Graceful connection closure
export async function closeDbConnections(): Promise<void> {
  try {
    if (sql) {
      await sql.end();
      logger.info('Database connections closed successfully');
    }
  } catch (error) {
    logger.error('Error closing database connections:', error);
  }
}

// Connection recovery mechanism
export async function reconnectDatabase(): Promise<void> {
  try {
    logger.info('Attempting database reconnection...');
    
    // Close existing connection
    if (sql) {
      await sql.end();
    }
    
    // Create new connection
    sql = await createConnection();
    
    logger.info('Database reconnection successful');
  } catch (error) {
    logger.error('Database reconnection failed:', error);
    throw error;
  }
}

// Enhanced execute function with automatic retry
export async function executeQuery<T>(
  queryFn: () => Promise<T>,
  retries: number = 3
): Promise<T> {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      return await queryFn();
    } catch (error: any) {
      logger.warn(`Query attempt ${attempt} failed:`, error.message);
      
      // Check if it's a connection error
      if (error.code === 'ECONNRESET' || error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
        if (attempt < retries) {
          logger.info(`Attempting query retry ${attempt + 1}/${retries}...`);
          await reconnectDatabase();
          continue;
        }
      }
      
      // Re-throw error if max retries reached or non-connection error
      throw error;
    }
  }
  
  throw new Error('Query failed after all retry attempts');
}

// Middleware for database connection health monitoring
export function dbHealthMiddleware(req: any, res: any, next: any) {
  // Check connection health periodically
  if (Math.random() < 0.01) { // 1% of requests trigger health check
    checkDatabaseHealth().then(isHealthy => {
      if (!isHealthy) {
        logger.warn('Database connection unhealthy, attempting reconnection');
        reconnectDatabase().catch(error => {
          logger.error('Failed to reconnect to database:', error);
        });
      }
    });
  }
  
  next();
}