Source-of-Truth Project Audit Prompt
You are tasked with conducting a comprehensive, source-of-truth project audit for three separate applications currently under development.
Do not suggest new features or improvements.
Focus exclusively on the current, actual state of each project.

Instructions:

Inventory All Existing Assets

List every codebase, repo, directory, and major module for each app.

For each, specify:

Primary language(s) and frameworks in use

Major dependencies and integrations (AI agents, APIs, third-party services)

Environment (dev, staging, prod, etc.) and deployment status

Status of Implementation

For each major functional area or feature, indicate:

What is fully implemented, tested, and working (with evidence: commit refs, tests, or screenshots if available)

What is partially implemented or in-progress (be specific: what’s done vs. what’s missing)

What is broken, non-functional, or known to be unreliable

Open Issues & Lingering Work

List all known bugs, blockers, or “works for now” kludges that still require resolution

Highlight any incomplete tasks, TODOs, or partial implementations

Surface any outdated dependencies, infrastructure concerns, or technical debt

Testing & Verification

Report what automated and manual tests exist for each project/module

Note code coverage (if measured) and summarize reliability

List any tests that fail or are currently skipped

Specify what is completely untested

Documentation & Knowledge Gaps

Summarize what internal/external documentation exists (README, code comments, architecture docs)

Call out areas where documentation is missing, out-of-date, or ambiguous

Summary Table

Deliver a summary table for each application with:

Module/Area

Status (Complete, Partial, Broken, Missing)

Comments (what’s working, what’s not, specific next step if unresolved)

No Feature Suggestions

Do not recommend, hypothesize, or speculate about new features, improvements, or future work.
This report is strictly a factual snapshot of what currently exists and what is unfinished or unreliable.

Your output should provide a single source of truth—a clear, actionable understanding of what’s real, what’s missing, and where work is needed.