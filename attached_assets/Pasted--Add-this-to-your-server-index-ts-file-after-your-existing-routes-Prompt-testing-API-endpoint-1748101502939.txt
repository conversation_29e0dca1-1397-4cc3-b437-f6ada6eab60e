// Add this to your server/index.ts file, after your existing routes

// Prompt testing API endpoint
app.post('/api/prompt-test', async (req, res) => {
  try {
    // Check if user is authenticated
    if (!req.session.userId) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { prompt, variables } = req.body;
    
    if (!prompt) {
      return res.status(400).json({ error: 'Prompt is required' });
    }

    // Process the prompt template with variables
    let processedPrompt = prompt;
    
    if (variables && typeof variables === 'object') {
      // Replace template variables (e.g., {{variable_name}})
      Object.entries(variables).forEach(([key, value]) => {
        const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
        processedPrompt = processedPrompt.replace(regex, String(value));
      });
    }

    // Simulate AI response (replace with actual AI service call)
    const mockResponses = [
      "Thank you for considering our dealership! I'd be happy to help you find the perfect vehicle. Based on your needs, I have several excellent options that might interest you. Would you like to schedule a test drive this week?",
      "I understand you're looking for the best value. Let me show you our current promotions and financing options that could save you thousands. We also have certified pre-owned vehicles with excellent warranties.",
      "Great choice! That model has been very popular this year. It comes with advanced safety features and excellent fuel efficiency. I can arrange for you to take one home today with our special financing program.",
      "I appreciate your interest! Let me check our inventory for the exact specifications you mentioned. We might have something even better than what you originally had in mind.",
      "Absolutely! We stand behind all our vehicles with comprehensive warranties. I can also show you our service department and explain our maintenance packages to keep your investment protected."
    ];

    const aiResponse = mockResponses[Math.floor(Math.random() * mockResponses.length)];

    // Store the test result in database (optional)
    const pool = await getPool();
    await pool.query(
      'INSERT INTO prompt_tests (user_id, original_prompt, processed_prompt, ai_response, variables, created_at) VALUES ($1, $2, $3, $4, $5, NOW())',
      [req.session.userId, prompt, processedPrompt, aiResponse, JSON.stringify(variables || {})]
    );

    res.json({
      success: true,
      processedPrompt,
      aiResponse,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Prompt test error:', error);
    res.status(500).json({ 
      error: 'Failed to test prompt',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

// Get prompt test history
app.get('/api/prompt-tests', async (req, res) => {
  try {
    if (!req.session.userId) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const pool = await getPool();
    const result = await pool.query(
      'SELECT id, original_prompt, processed_prompt, ai_response, variables, created_at FROM prompt_tests WHERE user_id = $1 ORDER BY created_at DESC LIMIT 50',
      [req.session.userId]
    );

    res.json({
      tests: result.rows
    });

  } catch (error) {
    console.error('Error fetching prompt tests:', error);
    res.status(500).json({ error: 'Failed to fetch prompt tests' });
  }
});