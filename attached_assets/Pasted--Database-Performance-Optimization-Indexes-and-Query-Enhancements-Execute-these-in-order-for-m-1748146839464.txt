-- Database Performance Optimization: Indexes and Query Enhancements
-- Execute these in order for maximum performance impact

-- ===================================
-- CONVERSATIONS TABLE OPTIMIZATION
-- ===================================

-- Index for conversation lookups by dealership (most common query)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_conversations_dealership_created 
ON conversations (dealership_id, created_at DESC);

-- Index for active conversations lookup
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_conversations_status_dealership 
ON conversations (status, dealership_id) 
WHERE status IN ('active', 'pending');

-- Index for customer conversation history
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_conversations_customer_dealership 
ON conversations (customer_id, dealership_id, created_at DESC);

-- Composite index for lead scoring queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_conversations_lead_scoring 
ON conversations (dealership_id, status, updated_at DESC) 
WHERE status = 'completed';

-- ===================================
-- MESSAGES TABLE OPTIMIZATION
-- ===================================

-- Index for message retrieval by conversation
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_messages_conversation_timestamp 
ON messages (conversation_id, created_at ASC);

-- Index for recent messages across dealership
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_messages_dealership_recent 
ON messages (dealership_id, created_at DESC) 
WHERE created_at > NOW() - INTERVAL '7 days';

-- Index for AI message analysis
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_messages_sender_type 
ON messages (conversation_id, sender_type, created_at ASC);

-- ===================================
-- USERS TABLE OPTIMIZATION
-- ===================================

-- Index for user authentication (login performance)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email_active 
ON users (email) 
WHERE is_active = true;

-- Index for username login
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_username_active 
ON users (username) 
WHERE is_active = true;

-- Index for dealership user management
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_dealership_role 
ON users (dealership_id, role) 
WHERE is_active = true;

-- ===================================
-- DEALERSHIPS TABLE OPTIMIZATION
-- ===================================

-- Index for active dealerships
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_dealerships_active 
ON dealerships (is_active, created_at DESC) 
WHERE is_active = true;

-- Index for dealership settings/branding lookups
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_dealerships_settings 
ON dealerships (id) 
INCLUDE (name, settings, branding_config);

-- ===================================
-- LEAD SCORING OPTIMIZATION
-- ===================================

-- Index for lead scores table (if exists)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_scores_conversation_score 
ON lead_scores (conversation_id, score DESC, calculated_at DESC);

-- Index for top leads by dealership
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lead_scores_dealership_top 
ON lead_scores (dealership_id, score DESC, calculated_at DESC) 
WHERE score > 0.7;

-- ===================================
-- FOLLOW-UPS TABLE OPTIMIZATION
-- ===================================

-- Index for user's follow-ups
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_follow_ups_assigned_user 
ON follow_ups (assigned_to, scheduled_time ASC) 
WHERE status IN ('pending', 'scheduled');

-- Index for dealership follow-ups dashboard
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_follow_ups_dealership_status 
ON follow_ups (dealership_id, status, scheduled_time ASC);

-- Index for overdue follow-ups
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_follow_ups_overdue 
ON follow_ups (scheduled_time ASC) 
WHERE status = 'pending' AND scheduled_time < NOW();

-- ===================================
-- SESSIONS TABLE OPTIMIZATION
-- ===================================

-- Index for session cleanup and retrieval
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_sessions_expire_time 
ON sessions (expire) 
WHERE expire > NOW();

-- Index for active user sessions
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_sessions_user_active 
ON sessions (data) 
WHERE (data->>'user'->>'id') IS NOT NULL;

-- ===================================
-- CUSTOMER PROFILES OPTIMIZATION
-- ===================================

-- Index for customer profile lookups
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customer_profiles_dealership_email 
ON customer_profiles (dealership_id, email);

-- Index for customer interaction tracking
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customer_interactions_profile_date 
ON customer_interactions (profile_id, interaction_date DESC);

-- ===================================
-- AUDIT LOGS OPTIMIZATION
-- ===================================

-- Index for audit log queries by dealership
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_dealership_date 
ON audit_logs (dealership_id, created_at DESC);

-- Index for user activity tracking
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_user_action 
ON audit_logs (user_id, action, created_at DESC);

-- ===================================
-- MAINTENANCE AND CLEANUP
-- ===================================

-- Add table statistics update for better query planning
ANALYZE conversations;
ANALYZE messages;
ANALYZE users;
ANALYZE dealerships;
ANALYZE lead_scores;
ANALYZE follow_ups;
ANALYZE customer_profiles;

-- ===================================
-- PERFORMANCE MONITORING QUERIES
-- ===================================

-- Query to check index usage
-- Run this periodically to ensure indexes are being used
/*
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan as index_scans,
    idx_tup_read as tuples_read,
    idx_tup_fetch as tuples_fetched
FROM pg_stat_user_indexes 
WHERE schemaname = 'public'
ORDER BY idx_scan DESC;
*/

-- Query to find slow queries (requires pg_stat_statements extension)
/*
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows
FROM pg_stat_statements 
WHERE query LIKE '%conversations%' 
   OR query LIKE '%messages%'
ORDER BY mean_time DESC 
LIMIT 10;
*/

-- Query to check table sizes and bloat
/*
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
*/