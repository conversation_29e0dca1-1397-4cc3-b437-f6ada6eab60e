import express, { Request, Response } from 'express';
import { db } from '../db';
import { sql } from 'drizzle-orm';
import { ResponseHelper, asyncHandler } from '../utils/errors';
import DealershipConfigService from '../services/dealership-config';
import logger from '../utils/logger';

const router = express.Router();
const configService = new DealershipConfigService();

// Get current dealership setup status
router.get('/status/:dealershipId', asyncHandler(async (req: Request, res: Response) => {
  const dealershipId = parseInt(req.params.dealershipId);

  try {
    const result = await db.execute(sql`
      SELECT 
        operation_mode,
        ai_config,
        agent_config,
        lead_routing,
        created_at
      FROM dealerships 
      WHERE id = ${dealershipId}
    `);

    if (result.rows.length === 0) {
      return ResponseHelper.notFound(res, 'Dealership');
    }

    const dealership = result.rows[0] as any;
    const isSetupComplete = dealership.operation_mode !== null;

    ResponseHelper.success(res, {
      isSetupComplete,
      mode: dealership.operation_mode,
      config: {
        ai: JSON.parse(dealership.ai_config || '{}'),
        agent: JSON.parse(dealership.agent_config || '{}'),
        leadRouting: JSON.parse(dealership.lead_routing || '{}')
      },
      setupDate: dealership.created_at
    });
  } catch (error) {
    const err = error instanceof Error ? error : new Error(String(error));
    logger.error('Error getting setup status', { error: err.message, dealershipId });
    ResponseHelper.error(res, err);
  }
}));

// Complete initial setup
router.post('/complete', asyncHandler(async (req: Request, res: Response) => {
  const {
    dealershipId,
    mode,
    dealershipInfo,
    rylieConfig,
    agentConfig,
    leadConfig
  } = req.body;

  if (!dealershipId || !mode) {
    return ResponseHelper.badRequest(res, 'Missing required fields: dealershipId, mode');
  }

  if (!['rylie_ai', 'direct_agent'].includes(mode)) {
    return ResponseHelper.badRequest(res, 'Invalid mode. Must be rylie_ai or direct_agent');
  }

  try {
    // Start transaction
    await db.execute(sql`BEGIN`);

    // Update dealership with setup configuration
    const updateQuery = sql`
      UPDATE dealerships SET
        operation_mode = ${mode},
        name = ${dealershipInfo.name},
        contact_email = ${dealershipInfo.email},
        contact_phone = ${dealershipInfo.phone || ''},
        website = ${dealershipInfo.website || ''},
        ai_config = ${JSON.stringify(mode === 'rylie_ai' ? rylieConfig : {})},
        agent_config = ${JSON.stringify(mode === 'direct_agent' ? agentConfig : {})},
        lead_routing = ${JSON.stringify(leadConfig)},
        updated_at = ${new Date().toISOString()}
      WHERE id = ${dealershipId}
    `;

    await db.execute(updateQuery);

    // Create default message templates if in direct agent mode
    if (mode === 'direct_agent') {
      await createDefaultTemplates(dealershipId, agentConfig);
    }

    // Create default lead sources
    await createDefaultLeadSources(dealershipId, mode);

    // Create default follow-up rules if automation is enabled
    if (leadConfig.followUpAutomation) {
      await createDefaultFollowUpRules(dealershipId);
    }

    // Commit transaction
    await db.execute(sql`COMMIT`);

    logger.info('Dealership setup completed', {
      dealershipId,
      mode,
      name: dealershipInfo.name
    });

    ResponseHelper.success(res, {
      message: 'Setup completed successfully',
      dealershipId,
      mode
    });

  } catch (error) {
    // Rollback transaction
    await db.execute(sql`ROLLBACK`);
    
    const err = error instanceof Error ? error : new Error(String(error));
    logger.error('Error completing setup', { error: err.message, dealershipId });
    ResponseHelper.error(res, err);
  }
}));

// Update operation mode
router.put('/mode/:dealershipId', asyncHandler(async (req: Request, res: Response) => {
  const dealershipId = parseInt(req.params.dealershipId);
  const { mode, config } = req.body;

  if (!['rylie_ai', 'direct_agent'].includes(mode)) {
    return ResponseHelper.badRequest(res, 'Invalid mode');
  }

  try {
    await configService.updateDealershipMode(dealershipId, mode, config);
    
    ResponseHelper.success(res, {
      message: 'Operation mode updated successfully',
      mode
    });
  } catch (error) {
    const err = error instanceof Error ? error : new Error(String(error));
    logger.error('Error updating mode', { error: err.message, dealershipId });
    ResponseHelper.error(res, err);
  }
}));

// Get available communication channels
router.get('/channels', asyncHandler(async (req: Request, res: Response) => {
  const channels = [
    {
      id: 'chat',
      name: 'Live Chat',
      description: 'Real-time chat widget on website',
      available: true,
      requirements: ['WebSocket support']
    },
    {
      id: 'email',
      name: 'Email',
      description: 'Email communication with customers',
      available: true,
      requirements: ['SMTP configuration']
    },
    {
      id: 'sms',
      name: 'SMS',
      description: 'Text messaging via SMS',
      available: !!process.env.TWILIO_ACCOUNT_SID,
      requirements: ['Twilio account']
    },
    {
      id: 'phone',
      name: 'Phone',
      description: 'Phone call integration',
      available: false,
      requirements: ['VoIP integration']
    }
  ];

  ResponseHelper.success(res, channels);
}));

// Test PureCars API connection
router.post('/test-purecars', asyncHandler(async (req: Request, res: Response) => {
  const { apiKey, endpoint } = req.body;

  if (!apiKey) {
    return ResponseHelper.badRequest(res, 'API key is required');
  }

  try {
    // Mock API test - replace with actual PureCars API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Simulate success/failure based on API key format
    const isValid = apiKey.length > 10 && apiKey.startsWith('pc_');
    
    if (isValid) {
      ResponseHelper.success(res, {
        connected: true,
        message: 'PureCars API connection successful',
        features: ['AI Responses', 'Lead Scoring', 'Intent Detection']
      });
    } else {
      ResponseHelper.success(res, {
        connected: false,
        message: 'Invalid API key or connection failed',
        error: 'Authentication failed'
      });
    }
  } catch (error) {
    const err = error instanceof Error ? error : new Error(String(error));
    logger.error('Error testing PureCars API', { error: err.message });
    ResponseHelper.error(res, err);
  }
}));

// Get setup wizard data
router.get('/wizard-data/:dealershipId', asyncHandler(async (req: Request, res: Response) => {
  const dealershipId = parseInt(req.params.dealershipId);

  try {
    const dealership = await db.execute(sql`
      SELECT 
        name, contact_email, contact_phone, website,
        operation_mode, ai_config, agent_config, lead_routing
      FROM dealerships 
      WHERE id = ${dealershipId}
    `);

    if (dealership.rows.length === 0) {
      return ResponseHelper.notFound(res, 'Dealership');
    }

    const data = dealership.rows[0] as any;
    
    ResponseHelper.success(res, {
      dealershipInfo: {
        name: data.name || '',
        email: data.contact_email || '',
        phone: data.contact_phone || '',
        website: data.website || ''
      },
      mode: data.operation_mode,
      aiConfig: JSON.parse(data.ai_config || '{}'),
      agentConfig: JSON.parse(data.agent_config || '{}'),
      leadConfig: JSON.parse(data.lead_routing || '{}')
    });
  } catch (error) {
    const err = error instanceof Error ? error : new Error(String(error));
    logger.error('Error getting wizard data', { error: err.message, dealershipId });
    ResponseHelper.error(res, err);
  }
}));

// Helper functions

async function createDefaultTemplates(dealershipId: number, agentConfig: any): Promise<void> {
  const templates = [
    {
      name: 'Greeting Message',
      subject: 'Welcome',
      content: agentConfig.templates?.greeting || 'Thank you for your message. An agent will be with you shortly.',
      channel: 'both',
      variables: ['customerName', 'dealershipName']
    },
    {
      name: 'Away Message',
      subject: 'Currently Offline',
      content: agentConfig.templates?.away || 'Thank you for your message. Our team is currently offline. We\'ll respond during our next business hours.',
      channel: 'both',
      variables: ['customerName', 'businessHours']
    },
    {
      name: 'Queue Message',
      subject: 'In Queue',
      content: agentConfig.templates?.queue || 'You\'re in the queue. We\'ll be with you soon!',
      channel: 'chat',
      variables: ['queuePosition', 'estimatedWait']
    },
    {
      name: 'Follow-up Email',
      subject: 'Following up on your inquiry',
      content: 'Hi {{customerName}}, I wanted to follow up on your recent inquiry about {{interestedVehicle}}. Is there anything specific you\'d like to know?',
      channel: 'email',
      variables: ['customerName', 'interestedVehicle', 'agentName']
    }
  ];

  for (const template of templates) {
    const templateId = `tpl_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    await db.execute(sql`
      INSERT INTO message_templates (
        id, dealership_id, name, subject, content, variables, channel
      ) VALUES (
        ${templateId}, ${dealershipId}, ${template.name}, ${template.subject},
        ${template.content}, ${JSON.stringify(template.variables)}, ${template.channel}
      )
    `);
  }
}

async function createDefaultLeadSources(dealershipId: number, mode: string): Promise<void> {
  const sources = [
    {
      name: 'Website Chat',
      type: 'chat',
      baseScore: mode === 'rylie_ai' ? 60 : 50,
      active: true
    },
    {
      name: 'Contact Form',
      type: 'website',
      baseScore: 70,
      active: true
    },
    {
      name: 'Phone Inquiry',
      type: 'phone',
      baseScore: 80,
      active: true
    },
    {
      name: 'Email Inquiry',
      type: 'email',
      baseScore: 65,
      active: true
    },
    {
      name: 'Social Media',
      type: 'social',
      baseScore: 55,
      active: true
    }
  ];

  for (const source of sources) {
    const sourceId = `src_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    await db.execute(sql`
      INSERT INTO lead_sources (
        id, dealership_id, name, type, base_score, active
      ) VALUES (
        ${sourceId}, ${dealershipId}, ${source.name}, ${source.type},
        ${source.baseScore}, ${source.active}
      )
    `);
  }
}

async function createDefaultFollowUpRules(dealershipId: number): Promise<void> {
  const rules = [
    {
      leadSource: 'Website Chat',
      sequence: [
        {
          stepNumber: 1,
          delayHours: 2,
          channel: 'email',
          templateId: 'default_followup_1'
        },
        {
          stepNumber: 2,
          delayHours: 24,
          channel: 'email',
          templateId: 'default_followup_2'
        },
        {
          stepNumber: 3,
          delayHours: 72,
          channel: 'sms',
          templateId: 'default_sms_followup'
        }
      ]
    },
    {
      leadSource: 'Contact Form',
      sequence: [
        {
          stepNumber: 1,
          delayHours: 1,
          channel: 'email',
          templateId: 'default_followup_1'
        },
        {
          stepNumber: 2,
          delayHours: 48,
          channel: 'email',
          templateId: 'default_followup_2'
        }
      ]
    }
  ];

  for (const rule of rules) {
    const ruleId = `rule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    await db.execute(sql`
      INSERT INTO follow_up_rules (
        id, dealership_id, lead_source, sequence, active
      ) VALUES (
        ${ruleId}, ${dealershipId}, ${rule.leadSource}, 
        ${JSON.stringify(rule.sequence)}, true
      )
    `);
  }
}

// Export router
export default router;