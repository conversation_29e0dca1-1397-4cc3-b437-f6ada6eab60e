import React from "react";
import { useAuth } from "@/hooks/useAuth";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useLocation } from "wouter";
import { BarChart3, TestTube, Users, LogOut } from "lucide-react";

export default function HomePage() {
  const { user, logout } = useAuth();
  const [, setLocation] = useLocation();

  const dashboardItems = [
    {
      id: "ai-prompt-testing",
      title: "AI Prompt Testing",
      description: "Test and refine AI responses with our advanced prompt testing tools.",
      icon: <TestTube className="h-6 w-6" />,
      color: "bg-blue-50 border-blue-200 hover:bg-blue-100",
      textColor: "text-blue-700",
      onClick: () => {
        // For now, show an alert - you can replace with actual routing
        alert("AI Prompt Testing feature coming soon!");
        // setLocation("/ai-testing");
      }
    },
    {
      id: "analytics-dashboard",
      title: "Analytics Dashboard",
      description: "View insights and performance metrics for your dealership.",
      icon: <BarChart3 className="h-6 w-6" />,
      color: "bg-green-50 border-green-200 hover:bg-green-100",
      textColor: "text-green-700",
      onClick: () => {
        alert("Analytics Dashboard feature coming soon!");
        // setLocation("/analytics");
      }
    },
    {
      id: "vendor-management",
      title: "Vendor Management",
      description: "Monitor and optimize your vendor relationships and integrations.",
      icon: <Users className="h-6 w-6" />,
      color: "bg-purple-50 border-purple-200 hover:bg-purple-100",
      textColor: "text-purple-700",
      onClick: () => {
        alert("Vendor Management feature coming soon!");
        // setLocation("/vendors");
      }
    }
  ];

  const handleLogout = () => {
    if (confirm("Are you sure you want to logout?")) {
      logout();
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <h1 className="text-xl font-semibold text-gray-900">
              Automotive Sales AI Platform
            </h1>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                Welcome, {user?.username || 'User'}
              </span>
              <Button 
                variant="outline" 
                size="sm"
                onClick={handleLogout}
                className="flex items-center space-x-2"
              >
                <LogOut className="h-4 w-4" />
                <span>Logout</span>
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Dashboard</h2>
          <p className="text-gray-600">Choose a feature to get started</p>
        </div>

        {/* Dashboard Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {dashboardItems.map((item) => (
            <Card 
              key={item.id}
              className={`cursor-pointer transition-all duration-200 ${item.color} hover:shadow-md`}
              onClick={item.onClick}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center space-x-3">
                  <div className={`${item.textColor}`}>
                    {item.icon}
                  </div>
                  <CardTitle className={`text-lg ${item.textColor}`}>
                    {item.title}
                  </CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-gray-600">
                  {item.description}
                </CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Additional Info */}
        <div className="mt-12 text-center">
          <p className="text-gray-500 text-sm">
            More features will be added to your dashboard as they become available.
          </p>
        </div>
      </main>
    </div>
  );
}