import { useState, useEffect, useRef, useCallback } from 'react';

export interface ChatMessage {
  id: string;
  conversationId: number;
  senderId: number;
  senderType: 'agent' | 'customer';
  content: string;
  messageType: 'text' | 'image' | 'file';
  timestamp: Date;
  metadata?: any;
}

export interface WebSocketMessage {
  type: string;
  [key: string]: any;
}

interface UseWebSocketOptions {
  url: string;
  autoConnect?: boolean;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
}

interface UseWebSocketReturn {
  isConnected: boolean;
  isConnecting: boolean;
  messages: ChatMessage[];
  sendMessage: (message: WebSocketMessage) => void;
  connect: () => void;
  disconnect: () => void;
  clearMessages: () => void;
  connectionId: string | null;
  lastActivity: Date | null;
}

export const useWebSocket = (options: UseWebSocketOptions): UseWebSocketReturn => {
  const {
    url,
    autoConnect = true,
    reconnectInterval = 3000,
    maxReconnectAttempts = 5
  } = options;

  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [connectionId, setConnectionId] = useState<string | null>(null);
  const [lastActivity, setLastActivity] = useState<Date | null>(null);

  const ws = useRef<WebSocket | null>(null);
  const reconnectAttempts = useRef(0);
  const reconnectTimeout = useRef<NodeJS.Timeout | null>(null);

  const connect = useCallback(() => {
    if (ws.current?.readyState === WebSocket.OPEN) {
      return;
    }

    setIsConnecting(true);

    try {
      ws.current = new WebSocket(url);

      ws.current.onopen = () => {
        console.log('WebSocket connected');
        setIsConnected(true);
        setIsConnecting(false);
        reconnectAttempts.current = 0;
        setLastActivity(new Date());
      };

      ws.current.onclose = (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason);
        setIsConnected(false);
        setIsConnecting(false);
        setConnectionId(null);

        // Attempt to reconnect if not a clean close
        if (event.code !== 1000 && reconnectAttempts.current < maxReconnectAttempts) {
          reconnectAttempts.current++;
          console.log(`Attempting to reconnect... (${reconnectAttempts.current}/${maxReconnectAttempts})`);
          
          reconnectTimeout.current = setTimeout(() => {
            connect();
          }, reconnectInterval * Math.pow(2, reconnectAttempts.current - 1)); // Exponential backoff
        }
      };

      ws.current.onerror = (error) => {
        console.error('WebSocket error:', error);
        setIsConnecting(false);
      };

      ws.current.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          setLastActivity(new Date());
          handleIncomingMessage(data);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

    } catch (error) {
      console.error('Error creating WebSocket connection:', error);
      setIsConnecting(false);
    }
  }, [url, maxReconnectAttempts, reconnectInterval]);

  const disconnect = useCallback(() => {
    if (reconnectTimeout.current) {
      clearTimeout(reconnectTimeout.current);
      reconnectTimeout.current = null;
    }

    if (ws.current) {
      ws.current.close(1000, 'User disconnected');
      ws.current = null;
    }

    setIsConnected(false);
    setIsConnecting(false);
    setConnectionId(null);
  }, []);

  const sendMessage = useCallback((message: WebSocketMessage) => {
    if (ws.current?.readyState === WebSocket.OPEN) {
      ws.current.send(JSON.stringify(message));
      setLastActivity(new Date());
    } else {
      console.warn('WebSocket is not connected. Message not sent:', message);
    }
  }, []);

  const handleIncomingMessage = (data: WebSocketMessage) => {
    switch (data.type) {
      case 'connection_established':
        setConnectionId(data.connectionId);
        console.log('Connection established with ID:', data.connectionId);
        break;

      case 'authenticated':
        console.log('User authenticated:', data.userType);
        break;

      case 'joined_conversation':
        console.log('Joined conversation:', data.conversationId);
        if (data.recentMessages && Array.isArray(data.recentMessages)) {
          setMessages(data.recentMessages);
        }
        break;

      case 'new_message':
        if (data.message) {
          setMessages(prev => [...prev, {
            ...data.message,
            timestamp: new Date(data.message.timestamp)
          }]);
        }
        break;

      case 'typing_indicator':
        // Handle typing indicators
        console.log('Typing indicator:', data);
        break;

      case 'user_joined':
        console.log('User joined:', data.userId, data.userType);
        break;

      case 'user_left':
        console.log('User left:', data.userId, data.userType);
        break;

      case 'error':
        console.error('WebSocket error:', data.error);
        break;

      case 'pong':
        // Handle ping/pong for connection health
        break;

      default:
        console.log('Unknown message type:', data.type, data);
    }
  };

  const clearMessages = useCallback(() => {
    setMessages([]);
  }, []);

  // Auto-connect on mount
  useEffect(() => {
    if (autoConnect) {
      connect();
    }

    return () => {
      disconnect();
    };
  }, [autoConnect, connect, disconnect]);

  // Send periodic pings to keep connection alive
  useEffect(() => {
    if (!isConnected) return;

    const pingInterval = setInterval(() => {
      sendMessage({ type: 'ping' });
    }, 30000); // Ping every 30 seconds

    return () => clearInterval(pingInterval);
  }, [isConnected, sendMessage]);

  return {
    isConnected,
    isConnecting,
    messages,
    sendMessage,
    connect,
    disconnect,
    clearMessages,
    connectionId,
    lastActivity
  };
};