import { db } from '../db';
import { eq, desc, asc, and, gte, lte, inArray, sql, count } from 'drizzle-orm';
import { users, dealerships } from '../../shared/schema';
import logger from '../utils/logger';
import { cacheService, createCacheKey } from './cache-service';

// Import User type from schema
import type { User } from '../../shared/schema';

// Define proper table references for conversations and messages
// These should match your actual database table structure
const conversationsTable = sql.identifier('conversations');
const messagesTable = sql.identifier('messages');

// ===================================
// OPTIMIZED CONVERSATION QUERIES
// ===================================

interface ConversationResult {
  id: number;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  status: string;
  createdAt: Date;
  updatedAt: Date;
  messageCount: number;
}

interface PaginatedConversations {
  conversations: ConversationResult[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

/**
 * Get recent conversations for a dealership with optimized pagination
 */
export async function getRecentConversations(
  dealershipId: number,
  page: number = 1,
  limit: number = 20,
  status?: string
): Promise<PaginatedConversations> {
  const offset = (page - 1) * limit;
  
  try {
    // Build the base query
    let whereConditions = [sql`dealership_id = ${dealershipId}`];
    
    if (status) {
      whereConditions.push(sql`status = ${status}`);
    }

    // Get conversations with message count
    const conversationsQuery = sql`
      SELECT 
        id,
        customer_name as "customerName",
        customer_email as "customerEmail", 
        customer_phone as "customerPhone",
        status,
        created_at as "createdAt",
        updated_at as "updatedAt",
        (SELECT COUNT(*) FROM messages WHERE conversation_id = conversations.id) as "messageCount"
      FROM conversations 
      WHERE ${sql.join(whereConditions, sql` AND `)}
      ORDER BY created_at DESC 
      LIMIT ${limit} OFFSET ${offset}
    `;

    // Get total count
    const countQuery = sql`
      SELECT COUNT(*) as total 
      FROM conversations 
      WHERE ${sql.join(whereConditions, sql` AND `)}
    `;

    const [conversations, totalResult] = await Promise.all([
      db.execute(conversationsQuery),
      db.execute(countQuery)
    ]);

    const total = Number((totalResult.rows[0] as any).total);

    return {
      conversations: conversations.rows as ConversationResult[],
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  } catch (error) {
    const err = error instanceof Error ? error : new Error(String(error));
    logger.error('Error fetching conversations', { error: err.message, dealershipId, page, limit, status });
    throw err;
  }
}

interface ConversationWithMessages {
  id: number;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  status: string;
  createdAt: Date;
  updatedAt: Date;
  messages: MessageResult[];
}

interface MessageResult {
  id: number;
  content: string;
  isFromCustomer: boolean;
  createdAt: Date;
  metadata: any;
}

/**
 * Get conversation with messages (optimized for chat display)
 */
export async function getConversationWithMessages(
  conversationId: number,
  messageLimit: number = 50
): Promise<ConversationWithMessages | null> {
  try {
    // Get conversation details
    const conversationQuery = sql`
      SELECT 
        id,
        customer_name as "customerName",
        customer_email as "customerEmail",
        customer_phone as "customerPhone", 
        status,
        created_at as "createdAt",
        updated_at as "updatedAt"
      FROM conversations 
      WHERE id = ${conversationId}
    `;

    // Get recent messages
    const messagesQuery = sql`
      SELECT 
        id,
        content,
        is_from_customer as "isFromCustomer",
        created_at as "createdAt",
        metadata
      FROM messages 
      WHERE conversation_id = ${conversationId}
      ORDER BY created_at DESC 
      LIMIT ${messageLimit}
    `;

    const [conversationResult, messagesResult] = await Promise.all([
      db.execute(conversationQuery),
      db.execute(messagesQuery)
    ]);

    if (conversationResult.rows.length === 0) {
      return null;
    }

    const conversation = conversationResult.rows[0] as any;
    const messages = (messagesResult.rows as MessageResult[]).reverse(); // Show oldest first for chat

    return {
      ...conversation,
      messages
    };
  } catch (error) {
    const err = error instanceof Error ? error : new Error(String(error));
    logger.error('Error fetching conversation with messages', { error: err.message, conversationId });
    throw err;
  }
}

/**
 * Get active conversations count by status (real-time dashboard)
 */
export async function getConversationCountsByStatus(dealershipId: number): Promise<Record<string, number>> {
  try {
    const query = sql`
      SELECT 
        status,
        COUNT(*) as count
      FROM conversations 
      WHERE dealership_id = ${dealershipId}
      GROUP BY status
    `;

    const result = await db.execute(query);
    
    // Transform to a more usable format
    const statusCounts: Record<string, number> = {};
    result.rows.forEach((row: any) => {
      statusCounts[row.status] = Number(row.count);
    });

    return statusCounts;
  } catch (error) {
    const err = error instanceof Error ? error : new Error(String(error));
    logger.error('Error fetching conversation counts by status', { error: err.message, dealershipId });
    throw err;
  }
}

// ===================================
// OPTIMIZED USER QUERIES
// ===================================

/**
 * Fast user authentication lookup
 */
export async function findUserForAuth(loginIdentifier: string): Promise<User | null> {
  try {
    // Use Drizzle ORM for user queries since they're properly defined in schema
    const user = await db
      .select({
        id: users.id,
        username: users.username,
        email: users.email,
        password: users.password,
        name: users.name,
        role: users.role,
        dealership_id: users.dealership_id,
        is_verified: users.is_verified,
        verification_token: users.verification_token,
        reset_token: users.reset_token,
        reset_token_expiry: users.reset_token_expiry,
        last_login: users.last_login,
        created_at: users.created_at,
        updated_at: users.updated_at
      })
      .from(users)
      .where(
        sql`(${users.email} = ${loginIdentifier} OR ${users.username} = ${loginIdentifier})`
      )
      .limit(1);

    return user[0] || null;
  } catch (error) {
    const err = error instanceof Error ? error : new Error(String(error));
    logger.error('Error finding user for auth', { error: err.message, loginIdentifier });
    throw err;
  }
}

interface DealershipUser {
  id: number;
  username: string | null;
  email: string;
  name: string | null;
  role: string;
  isVerified: boolean;
  createdAt: Date;
}

/**
 * Get dealership users with roles (optimized for admin dashboard)
 */
export async function getDealershipUsers(
  dealershipId: number,
  includeUnverified: boolean = false
): Promise<DealershipUser[]> {
  try {
    let whereConditions = [eq(users.dealership_id, dealershipId)];
    
    if (!includeUnverified) {
      whereConditions.push(eq(users.is_verified, true));
    }

    const result = await db
      .select({
        id: users.id,
        username: users.username,
        email: users.email,
        name: users.name,
        role: users.role,
        isVerified: users.is_verified,
        createdAt: users.created_at
      })
      .from(users)
      .where(and(...whereConditions))
      .orderBy(asc(users.role), asc(users.name));

    return result as DealershipUser[];
  } catch (error) {
    const err = error instanceof Error ? error : new Error(String(error));
    logger.error('Error fetching dealership users', { error: err.message, dealershipId });
    throw err;
  }
}

// ===================================
// OPTIMIZED SEARCH QUERIES
// ===================================

interface SearchResult {
  id: number;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  status: string;
  createdAt: Date;
}

/**
 * Search conversations by customer information
 */
export async function searchConversations(
  dealershipId: number,
  searchTerm: string,
  limit: number = 20
): Promise<SearchResult[]> {
  try {
    const searchPattern = `%${searchTerm}%`;
    
    const query = sql`
      SELECT 
        id,
        customer_name as "customerName",
        customer_email as "customerEmail",
        customer_phone as "customerPhone",
        status,
        created_at as "createdAt"
      FROM conversations 
      WHERE dealership_id = ${dealershipId}
        AND (
          customer_name ILIKE ${searchPattern} OR 
          customer_email ILIKE ${searchPattern} OR 
          customer_phone ILIKE ${searchPattern}
        )
      ORDER BY created_at DESC 
      LIMIT ${limit}
    `;

    const result = await db.execute(query);
    return result.rows as SearchResult[];
  } catch (error) {
    const err = error instanceof Error ? error : new Error(String(error));
    logger.error('Error searching conversations', { error: err.message, dealershipId, searchTerm });
    throw err;
  }
}

interface MessageSearchResult {
  id: number;
  content: string;
  conversationId: number;
  createdAt: Date;
  customerName: string;
}

/**
 * Search messages content (using text search)
 */
export async function searchMessageContent(
  dealershipId: number,
  searchTerm: string,
  limit: number = 20
): Promise<MessageSearchResult[]> {
  try {
    // Simple text search - can be enhanced with PostgreSQL full-text search later
    const searchPattern = `%${searchTerm}%`;
    
    const query = sql`
      SELECT 
        m.id,
        m.content,
        m.conversation_id as "conversationId",
        m.created_at as "createdAt",
        c.customer_name as "customerName"
      FROM messages m
      INNER JOIN conversations c ON m.conversation_id = c.id
      WHERE c.dealership_id = ${dealershipId}
        AND m.content ILIKE ${searchPattern}
      ORDER BY m.created_at DESC 
      LIMIT ${limit}
    `;

    const result = await db.execute(query);
    return result.rows as MessageSearchResult[];
  } catch (error) {
    const err = error instanceof Error ? error : new Error(String(error));
    logger.error('Error searching message content', { error: err.message, dealershipId, searchTerm });
    throw err;
  }
}

// ===================================
// ANALYTICS AND REPORTING QUERIES
// ===================================

interface AnalyticsResult {
  date: string;
  totalConversations: number;
  messagesCount: number;
}

/**
 * Get conversation analytics for a time period
 */
export async function getConversationAnalytics(
  dealershipId: number,
  startDate: Date,
  endDate: Date
): Promise<AnalyticsResult[]> {
  try {
    const query = sql`
      SELECT 
        DATE(c.created_at) as date,
        COUNT(DISTINCT c.id) as "totalConversations",
        COUNT(m.id) as "messagesCount"
      FROM conversations c
      LEFT JOIN messages m ON c.id = m.conversation_id
      WHERE c.dealership_id = ${dealershipId}
        AND c.created_at >= ${startDate.toISOString()}
        AND c.created_at <= ${endDate.toISOString()}
      GROUP BY DATE(c.created_at)
      ORDER BY date
    `;

    const result = await db.execute(query);
    return result.rows as AnalyticsResult[];
  } catch (error) {
    const err = error instanceof Error ? error : new Error(String(error));
    logger.error('Error fetching conversation analytics', { error: err.message, dealershipId });
    throw err;
  }
}

// Export all optimized functions
export {
  getRecentConversations,
  getConversationWithMessages,
  getConversationCountsByStatus,
  findUserForAuth,
  getDealershipUsers,
  searchConversations,
  searchMessageContent,
  getConversationAnalytics
};