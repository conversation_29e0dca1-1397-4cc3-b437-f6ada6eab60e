// Fix import patterns across all service files

// 1. server/services/optimized-queries.ts - Fix imports
import db from '../db'; // Changed from { db }
import { eq, desc, asc, and, gte, lte, inArray, sql, count } from 'drizzle-orm';
import { users, dealerships } from '../../shared/schema';
import logger from '../utils/logger';
import { cacheService, createCacheKey } from './cache-service';
import type { User } from '../../shared/schema';

// 2. server/services/dealership-config.ts - Fix imports
import db from '../db'; // Changed from { db }
import { sql } from 'drizzle-orm';
import logger from '../utils/logger';
import { cacheService } from './redis-cache';

// 3. server/services/conversation-router.ts - Fix imports
import logger from '../utils/logger';
import DealershipConfigService from './dealership-config';
import MessagingService from './messaging-service';
import LeadManagementService from './lead-management';
import ChatServer from './chat-server';
import db from '../db'; // Changed from { db }
import { sql } from 'drizzle-orm';

// 4. server/services/messaging-service.ts - Fix imports
import nodemailer from 'nodemailer';
import logger from '../utils/logger';
import db from '../db'; // Changed from { db }
import { sql } from 'drizzle-orm';

// 5. server/services/lead-management.ts - Fix imports
import db from '../db'; // Changed from { db }
import { sql } from 'drizzle-orm';
import logger from '../utils/logger';
import MessagingService from './messaging-service';
import { cacheService } from './redis-cache';

// 6. server/services/chat-server.ts - Fix imports
import { WebSocketServer, WebSocket } from 'ws';
import { Server } from 'http';
import logger from '../utils/logger';
import { verifySession } from '../middleware/auth';
import db from '../db'; // Changed from { db }
import { sql } from 'drizzle-orm';
import { v4 as uuidv4 } from 'uuid';

// 7. server/routes/setup-routes.ts - Fix imports
import express, { Request, Response } from 'express';
import db from '../db'; // Changed from { db }
import { sql } from 'drizzle-orm';
import { ResponseHelper, asyncHandler } from '../utils/errors';
import DealershipConfigService from '../services/dealership-config';
import logger from '../utils/logger';

// 8. server/routes/performance-routes.ts - Fix imports
import express, { Request, Response } from 'express';
import { cacheService, createCacheKey } from '../services/cache-service';
import {
  getRecentConversations,
  getConversationWithMessages,
  searchConversations,
  getConversationAnalytics
} from '../services/optimized-queries';
import logger from '../utils/logger';

// 9. server/routes/monitoring-routes.ts - Fix imports
import express, { Request, Response } from 'express';
import { cacheService } from '../services/redis-cache';
import { checkDatabaseHealth } from '../db'; // Named import for health check
import logger from '../utils/logger';
import { ResponseHelper, asyncHandler } from '../utils/errors';
import os from 'os';

// 10. Any other route files should follow this pattern:
// import db from '../db'; // Default import
// import { checkDatabaseHealth, executeQuery } from '../db'; // Named imports for utilities