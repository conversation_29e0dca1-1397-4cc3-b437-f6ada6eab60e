import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Bot, Users, MessageSquare, Phone, Mail, Clock, Zap, Settings, CheckCircle } from 'lucide-react';

interface SetupData {
  mode: 'rylie_ai' | 'direct_agent' | null;
  dealershipInfo: {
    name: string;
    email: string;
    phone: string;
    website: string;
  };
  rylieConfig: {
    purecarsApiKey: string;
    personality: string;
    responseDelay: number;
  };
  agentConfig: {
    enabledChannels: string[];
    autoAssignment: boolean;
    workingHours: any;
    templates: {
      greeting: string;
      away: string;
      queue: string;
    };
  };
  leadConfig: {
    autoCreateLeads: boolean;
    assignmentStrategy: string;
    followUpAutomation: boolean;
  };
}

const DealershipSetupWizard: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [setupData, setSetupData] = useState<SetupData>({
    mode: null,
    dealershipInfo: {
      name: '',
      email: '',
      phone: '',
      website: ''
    },
    rylieConfig: {
      purecarsApiKey: '',
      personality: 'friendly',
      responseDelay: 1000
    },
    agentConfig: {
      enabledChannels: ['chat', 'email'],
      autoAssignment: false,
      workingHours: {
        timezone: 'America/New_York',
        schedule: {
          monday: { start: '09:00', end: '17:00', enabled: true },
          tuesday: { start: '09:00', end: '17:00', enabled: true },
          wednesday: { start: '09:00', end: '17:00', enabled: true },
          thursday: { start: '09:00', end: '17:00', enabled: true },
          friday: { start: '09:00', end: '17:00', enabled: true },
          saturday: { start: '10:00', end: '16:00', enabled: true },
          sunday: { start: '12:00', end: '16:00', enabled: false }
        }
      },
      templates: {
        greeting: 'Thank you for your message. An agent will be with you shortly.',
        away: 'Thank you for your message. Our team is currently offline. We\'ll respond during our next business hours.',
        queue: 'You\'re in the queue. We\'ll be with you soon!'
      }
    },
    leadConfig: {
      autoCreateLeads: true,
      assignmentStrategy: 'round_robin',
      followUpAutomation: true
    }
  });

  const steps = [
    { id: 'mode', title: 'Choose Your Mode', icon: Settings },
    { id: 'info', title: 'Dealership Info', icon: Users },
    { id: 'config', title: 'Configuration', icon: Zap },
    { id: 'review', title: 'Review & Complete', icon: CheckCircle }
  ];

  const handleModeSelect = (mode: 'rylie_ai' | 'direct_agent') => {
    setSetupData(prev => ({ ...prev, mode }));
    setCurrentStep(1);
  };

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSave = async () => {
    try {
      const response = await fetch('/api/dealership/setup', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(setupData)
      });

      if (response.ok) {
        alert('Setup completed successfully!');
        // Redirect to dashboard
      } else {
        alert('Setup failed. Please try again.');
      }
    } catch (error) {
      console.error('Setup error:', error);
      alert('Setup failed. Please try again.');
    }
  };

  const ModeSelection = () => (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold mb-2">Choose Your Operation Mode</h2>
        <p className="text-gray-600">How would you like to handle customer conversations?</p>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        {/* Rylie AI Mode */}
        <Card 
          className={`cursor-pointer transition-all hover:shadow-lg ${
            setupData.mode === 'rylie_ai' ? 'ring-2 ring-blue-500 border-blue-500' : ''
          }`}
          onClick={() => handleModeSelect('rylie_ai')}
        >
          <CardHeader className="text-center">
            <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
              <Bot className="w-8 h-8 text-blue-600" />
            </div>
            <CardTitle className="text-xl">Rylie AI Mode</CardTitle>
            <CardDescription>
              Automated AI responses using PureCars integration
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span className="text-sm">24/7 automated responses</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span className="text-sm">AI-powered lead qualification</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span className="text-sm">Instant response times</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span className="text-sm">Automatic escalation to agents</span>
              </div>
            </div>
            <div className="mt-4 p-3 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-700">
                <strong>Best for:</strong> Dealerships wanting automated customer engagement with AI intelligence
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Direct Agent Mode */}
        <Card 
          className={`cursor-pointer transition-all hover:shadow-lg ${
            setupData.mode === 'direct_agent' ? 'ring-2 ring-green-500 border-green-500' : ''
          }`}
          onClick={() => handleModeSelect('direct_agent')}
        >
          <CardHeader className="text-center">
            <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <Users className="w-8 h-8 text-green-600" />
            </div>
            <CardTitle className="text-xl">Direct Agent Mode</CardTitle>
            <CardDescription>
              Human agents handle all customer communications
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span className="text-sm">Personal human touch</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span className="text-sm">Real-time chat, email & SMS</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span className="text-sm">Customizable working hours</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span className="text-sm">Advanced lead routing</span>
              </div>
            </div>
            <div className="mt-4 p-3 bg-green-50 rounded-lg">
              <p className="text-sm text-green-700">
                <strong>Best for:</strong> Dealerships preferring direct human interaction and control
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const DealershipInfoStep = () => (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold mb-2">Dealership Information</h2>
        <p className="text-gray-600">Basic information about your dealership</p>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <div>
            <Label htmlFor="name">Dealership Name *</Label>
            <Input
              id="name"
              value={setupData.dealershipInfo.name}
              onChange={(e) => setSetupData(prev => ({
                ...prev,
                dealershipInfo: { ...prev.dealershipInfo, name: e.target.value }
              }))}
              placeholder="Enter dealership name"
            />
          </div>
          <div>
            <Label htmlFor="email">Contact Email *</Label>
            <Input
              id="email"
              type="email"
              value={setupData.dealershipInfo.email}
              onChange={(e) => setSetupData(prev => ({
                ...prev,
                dealershipInfo: { ...prev.dealershipInfo, email: e.target.value }
              }))}
              placeholder="<EMAIL>"
            />
          </div>
        </div>
        <div className="space-y-4">
          <div>
            <Label htmlFor="phone">Contact Phone</Label>
            <Input
              id="phone"
              value={setupData.dealershipInfo.phone}
              onChange={(e) => setSetupData(prev => ({
                ...prev,
                dealershipInfo: { ...prev.dealershipInfo, phone: e.target.value }
              }))}
              placeholder="+****************"
            />
          </div>
          <div>
            <Label htmlFor="website">Website</Label>
            <Input
              id="website"
              value={setupData.dealershipInfo.website}
              onChange={(e) => setSetupData(prev => ({
                ...prev,
                dealershipInfo: { ...prev.dealershipInfo, website: e.target.value }
              }))}
              placeholder="https://www.dealership.com"
            />
          </div>
        </div>
      </div>
    </div>
  );

  const ConfigurationStep = () => (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold mb-2">Configuration</h2>
        <p className="text-gray-600">
          Configure your {setupData.mode === 'rylie_ai' ? 'AI assistant' : 'agent system'}
        </p>
      </div>

      {setupData.mode === 'rylie_ai' ? (
        <Tabs defaultValue="ai" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="ai">AI Settings</TabsTrigger>
            <TabsTrigger value="leads">Lead Management</TabsTrigger>
          </TabsList>
          
          <TabsContent value="ai" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>AI Configuration</CardTitle>
                <CardDescription>Configure your Rylie AI assistant</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="apiKey">PureCars API Key *</Label>
                  <Input
                    id="apiKey"
                    type="password"
                    value={setupData.rylieConfig.purecarsApiKey}
                    onChange={(e) => setSetupData(prev => ({
                      ...prev,
                      rylieConfig: { ...prev.rylieConfig, purecarsApiKey: e.target.value }
                    }))}
                    placeholder="Enter your PureCars API key"
                  />
                </div>
                <div>
                  <Label htmlFor="personality">AI Personality</Label>
                  <Select 
                    value={setupData.rylieConfig.personality}
                    onValueChange={(value) => setSetupData(prev => ({
                      ...prev,
                      rylieConfig: { ...prev.rylieConfig, personality: value }
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="friendly">Friendly & Casual</SelectItem>
                      <SelectItem value="professional">Professional</SelectItem>
                      <SelectItem value="enthusiastic">Enthusiastic</SelectItem>
                      <SelectItem value="helpful">Helpful & Informative</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="delay">Response Delay (ms)</Label>
                  <Input
                    id="delay"
                    type="number"
                    value={setupData.rylieConfig.responseDelay}
                    onChange={(e) => setSetupData(prev => ({
                      ...prev,
                      rylieConfig: { ...prev.rylieConfig, responseDelay: parseInt(e.target.value) }
                    }))}
                    placeholder="1000"
                  />
                  <p className="text-xs text-gray-500 mt-1">Simulates human response time</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="leads" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Lead Management</CardTitle>
                <CardDescription>Configure how leads are handled</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label>Auto-create leads</Label>
                    <p className="text-xs text-gray-500">Automatically create leads from conversations</p>
                  </div>
                  <Switch
                    checked={setupData.leadConfig.autoCreateLeads}
                    onCheckedChange={(checked) => setSetupData(prev => ({
                      ...prev,
                      leadConfig: { ...prev.leadConfig, autoCreateLeads: checked }
                    }))}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <Label>Follow-up automation</Label>
                    <p className="text-xs text-gray-500">Enable automated follow-up sequences</p>
                  </div>
                  <Switch
                    checked={setupData.leadConfig.followUpAutomation}
                    onCheckedChange={(checked) => setSetupData(prev => ({
                      ...prev,
                      leadConfig: { ...prev.leadConfig, followUpAutomation: checked }
                    }))}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      ) : (
        <Tabs defaultValue="channels" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="channels">Channels</TabsTrigger>
            <TabsTrigger value="hours">Working Hours</TabsTrigger>
            <TabsTrigger value="templates">Templates</TabsTrigger>
          </TabsList>

          <TabsContent value="channels" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Communication Channels</CardTitle>
                <CardDescription>Select which channels to enable</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  {[
                    { id: 'chat', label: 'Live Chat', icon: MessageSquare },
                    { id: 'email', label: 'Email', icon: Mail },
                    { id: 'sms', label: 'SMS', icon: Phone },
                    { id: 'phone', label: 'Phone', icon: Phone }
                  ].map(channel => {
                    const Icon = channel.icon;
                    return (
                      <div key={channel.id} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id={channel.id}
                          checked={setupData.agentConfig.enabledChannels.includes(channel.id)}
                          onChange={(e) => {
                            const channels = e.target.checked
                              ? [...setupData.agentConfig.enabledChannels, channel.id]
                              : setupData.agentConfig.enabledChannels.filter(c => c !== channel.id);
                            setSetupData(prev => ({
                              ...prev,
                              agentConfig: { ...prev.agentConfig, enabledChannels: channels }
                            }));
                          }}
                          className="rounded"
                        />
                        <Icon className="w-4 h-4" />
                        <Label htmlFor={channel.id}>{channel.label}</Label>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="hours" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Working Hours</CardTitle>
                <CardDescription>Set your dealership's operating hours</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Object.entries(setupData.agentConfig.workingHours.schedule).map(([day, schedule]: [string, any]) => (
                    <div key={day} className="flex items-center gap-4">
                      <div className="w-20">
                        <Label className="capitalize">{day}</Label>
                      </div>
                      <Switch
                        checked={schedule.enabled}
                        onCheckedChange={(checked) => {
                          setSetupData(prev => ({
                            ...prev,
                            agentConfig: {
                              ...prev.agentConfig,
                              workingHours: {
                                ...prev.agentConfig.workingHours,
                                schedule: {
                                  ...prev.agentConfig.workingHours.schedule,
                                  [day]: { ...schedule, enabled: checked }
                                }
                              }
                            }
                          }));
                        }}
                      />
                      {schedule.enabled && (
                        <>
                          <Input
                            type="time"
                            value={schedule.start}
                            onChange={(e) => {
                              setSetupData(prev => ({
                                ...prev,
                                agentConfig: {
                                  ...prev.agentConfig,
                                  workingHours: {
                                    ...prev.agentConfig.workingHours,
                                    schedule: {
                                      ...prev.agentConfig.workingHours.schedule,
                                      [day]: { ...schedule, start: e.target.value }
                                    }
                                  }
                                }
                              }));
                            }}
                            className="w-24"
                          />
                          <span>to</span>
                          <Input
                            type="time"
                            value={schedule.end}
                            onChange={(e) => {
                              setSetupData(prev => ({
                                ...prev,
                                agentConfig: {
                                  ...prev.agentConfig,
                                  workingHours: {
                                    ...prev.agentConfig.workingHours,
                                    schedule: {
                                      ...prev.agentConfig.workingHours.schedule,
                                      [day]: { ...schedule, end: e.target.value }
                                    }
                                  }
                                }
                              }));
                            }}
                            className="w-24"
                          />
                        </>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="templates" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Message Templates</CardTitle>
                <CardDescription>Customize automatic messages</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="greeting">Greeting Message</Label>
                  <Textarea
                    id="greeting"
                    value={setupData.agentConfig.templates.greeting}
                    onChange={(e) => setSetupData(prev => ({
                      ...prev,
                      agentConfig: {
                        ...prev.agentConfig,
                        templates: { ...prev.agentConfig.templates, greeting: e.target.value }
                      }
                    }))}
                    placeholder="Thank you for your message..."
                  />
                </div>
                <div>
                  <Label htmlFor="away">Away Message</Label>
                  <Textarea
                    id="away"
                    value={setupData.agentConfig.templates.away}
                    onChange={(e) => setSetupData(prev => ({
                      ...prev,
                      agentConfig: {
                        ...prev.agentConfig,
                        templates: { ...prev.agentConfig.templates, away: e.target.value }
                      }
                    }))}
                    placeholder="Our team is currently offline..."
                  />
                </div>
                <div>
                  <Label htmlFor="queue">Queue Message</Label>
                  <Textarea
                    id="queue"
                    value={setupData.agentConfig.templates.queue}
                    onChange={(e) => setSetupData(prev => ({
                      ...prev,
                      agentConfig: {
                        ...prev.agentConfig,
                        templates: { ...prev.agentConfig.templates, queue: e.target.value }
                      }
                    }))}
                    placeholder="You're in the queue..."
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );

  const ReviewStep = () => (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold mb-2">Review Your Setup</h2>
        <p className="text-gray-600">Please review your configuration before completing setup</p>
      </div>

      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {setupData.mode === 'rylie_ai' ? <Bot className="w-5 h-5" /> : <Users className="w-5 h-5" />}
              Operation Mode
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Badge variant={setupData.mode === 'rylie_ai' ? 'default' : 'secondary'}>
                {setupData.mode === 'rylie_ai' ? 'Rylie AI Mode' : 'Direct Agent Mode'}
              </Badge>
              <span className="text-sm text-gray-600">
                {setupData.mode === 'rylie_ai' 
                  ? 'Automated AI responses with PureCars integration'
                  : 'Human agents handle all communications'
                }
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Dealership Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <strong>Name:</strong> {setupData.dealershipInfo.name}
              </div>
              <div>
                <strong>Email:</strong> {setupData.dealershipInfo.email}
              </div>
              <div>
                <strong>Phone:</strong> {setupData.dealershipInfo.phone || 'Not provided'}
              </div>
              <div>
                <strong>Website:</strong> {setupData.dealershipInfo.website || 'Not provided'}
              </div>
            </div>
          </CardContent>
        </Card>

        {setupData.mode === 'rylie_ai' ? (
          <Card>
            <CardHeader>
              <CardTitle>AI Configuration</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div><strong>API Key:</strong> {setupData.rylieConfig.purecarsApiKey ? '••••••••' : 'Not configured'}</div>
                <div><strong>Personality:</strong> {setupData.rylieConfig.personality}</div>
                <div><strong>Response Delay:</strong> {setupData.rylieConfig.responseDelay}ms</div>
              </div>
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle>Agent Configuration</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div>
                  <strong>Enabled Channels:</strong> 
                  <div className="flex gap-2 mt-1">
                    {setupData.agentConfig.enabledChannels.map(channel => (
                      <Badge key={channel} variant="outline">{channel}</Badge>
                    ))}
                  </div>
                </div>
                <div><strong>Auto Assignment:</strong> {setupData.agentConfig.autoAssignment ? 'Enabled' : 'Disabled'}</div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Progress Steps */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => {
            const Icon = step.icon;
            const isActive = index === currentStep;
            const isCompleted = index < currentStep;
            
            return (
              <div key={step.id} className="flex items-center">
                <div className={`
                  flex items-center justify-center w-10 h-10 rounded-full border-2 
                  ${isActive ? 'border-blue-500 bg-blue-500 text-white' : 
                    isCompleted ? 'border-green-500 bg-green-500 text-white' : 
                    'border-gray-300 bg-white text-gray-400'}
                `}>
                  <Icon className="w-5 h-5" />
                </div>
                <div className="ml-2 hidden md:block">
                  <div className={`text-sm font-medium ${isActive ? 'text-blue-600' : isCompleted ? 'text-green-600' : 'text-gray-400'}`}>
                    {step.title}
                  </div>
                </div>
                {index < steps.length - 1 && (
                  <div className={`w-12 h-1 mx-4 ${isCompleted ? 'bg-green-500' : 'bg-gray-300'}`} />
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Step Content */}
      <Card className="min-h-[600px]">
        <CardContent className="p-8">
          {currentStep === 0 && <ModeSelection />}
          {currentStep === 1 && <DealershipInfoStep />}
          {currentStep === 2 && <ConfigurationStep />}
          {currentStep === 3 && <ReviewStep />}
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between mt-6">
        <Button 
          variant="outline" 
          onClick={handleBack}
          disabled={currentStep === 0}
        >
          Back
        </Button>
        
        {currentStep < steps.length - 1 ? (
          <Button 
            onClick={handleNext}
            disabled={!setupData.mode || (currentStep === 1 && !setupData.dealershipInfo.name)}
          >
            Next
          </Button>
        ) : (
          <Button onClick={handleSave}>
            Complete Setup
          </Button>
        )}
      </div>
    </div>
  );
};

export default DealershipSetupWizard;