import logger from '../utils/logger';
import DealershipConfigService from './dealership-config';
import MessagingService from './messaging-service';
import LeadManagementService from './lead-management';
import ChatServer from './chat-server';
import { db } from '../db';
import { sql } from 'drizzle-orm';

interface IncomingMessage {
  dealershipId: number;
  customerName?: string;
  customerEmail?: string;
  customerPhone?: string;
  content: string;
  channel: 'chat' | 'email' | 'sms' | 'phone';
  conversationId?: number;
  source: string;
  metadata?: any;
}

interface ConversationResponse {
  success: boolean;
  conversationId: number;
  response?: string;
  action: 'ai_response' | 'agent_queue' | 'agent_direct' | 'auto_reply';
  estimatedWaitTime?: number;
  assignedAgent?: number;
}

export class ConversationRouter {
  private configService: DealershipConfigService;
  private messagingService: MessagingService;
  private leadService: LeadManagementService;
  private chatServer: ChatServer;

  constructor(chatServer: ChatServer) {
    this.configService = new DealershipConfigService();
    this.messagingService = new MessagingService();
    this.leadService = new LeadManagementService();
    this.chatServer = chatServer;
  }

  async routeIncomingMessage(message: IncomingMessage): Promise<ConversationResponse> {
    try {
      logger.info('Routing incoming message', {
        dealershipId: message.dealershipId,
        channel: message.channel,
        source: message.source
      });

      // Get dealership configuration
      const config = await this.configService.getDealershipConfig(message.dealershipId);
      
      // Find or create conversation
      const conversationId = await this.findOrCreateConversation(message);
      
      // Route based on dealership mode
      if (config.mode === 'rylie_ai') {
        return await this.handleRylieMode(message, conversationId, config);
      } else {
        return await this.handleDirectAgentMode(message, conversationId, config);
      }

    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error('Error routing message', { 
        error: err.message, 
        dealershipId: message.dealershipId 
      });
      
      return {
        success: false,
        conversationId: message.conversationId || 0,
        action: 'auto_reply',
        response: 'Sorry, we encountered an error. Please try again later.'
      };
    }
  }

  private async handleRylieMode(
    message: IncomingMessage, 
    conversationId: number, 
    config: any
  ): Promise<ConversationResponse> {
    try {
      // Call PureCars API for AI response
      const aiResponse = await this.callPureCarsAPI(message, config.aiConfig);
      
      if (aiResponse.success) {
        // Save the AI response as a message
        await this.saveMessage(conversationId, aiResponse.response, false);
        
        // Create/update lead if configured
        if (config.leadRouting.auto_create_leads) {
          await this.createOrUpdateLead(message, conversationId);
        }

        return {
          success: true,
          conversationId,
          response: aiResponse.response,
          action: 'ai_response'
        };
      } else {
        // AI failed, fallback to agent queue
        logger.warn('AI response failed, falling back to agent', {
          conversationId,
          error: aiResponse.error
        });
        
        return await this.queueForAgent(message, conversationId, config);
      }

    } catch (error) {
      logger.error('Error in Rylie mode', { error, conversationId });
      return await this.queueForAgent(message, conversationId, config);
    }
  }

  private async handleDirectAgentMode(
    message: IncomingMessage, 
    conversationId: number, 
    config: any
  ): Promise<ConversationResponse> {
    try {
      // Check if within working hours
      const isWorkingHours = await this.configService.isWithinWorkingHours(message.dealershipId);
      
      if (!isWorkingHours) {
        // Send auto-reply for outside hours
        const awayMessage = config.agentConfig.templates.away_message || 
          'Thank you for your message. Our team is currently offline. We\'ll respond during our next business hours.';
        
        await this.saveMessage(conversationId, awayMessage, false);
        
        // Still create lead for follow-up
        if (config.leadRouting.auto_create_leads) {
          await this.createOrUpdateLead(message, conversationId);
        }

        return {
          success: true,
          conversationId,
          response: awayMessage,
          action: 'auto_reply'
        };
      }

      // Try to assign to available agent
      const assignedAgent = await this.assignToAgent(message.dealershipId, conversationId, config);
      
      if (assignedAgent) {
        // Direct assignment to agent
        await this.notifyAgent(assignedAgent, conversationId, message);
        
        const directMessage = config.agentConfig.templates.greeting_message || 
          'Thank you for your message. An agent will be with you shortly.';
        
        await this.saveMessage(conversationId, directMessage, false);

        return {
          success: true,
          conversationId,
          response: directMessage,
          action: 'agent_direct',
          assignedAgent
        };
      } else {
        // Queue for next available agent
        return await this.queueForAgent(message, conversationId, config);
      }

    } catch (error) {
      logger.error('Error in direct agent mode', { error, conversationId });
      
      // Fallback to auto-reply
      const errorMessage = 'We\'re experiencing high volume. Please leave your contact information and we\'ll get back to you soon.';
      await this.saveMessage(conversationId, errorMessage, false);
      
      return {
        success: true,
        conversationId,
        response: errorMessage,
        action: 'auto_reply'
      };
    }
  }

  private async callPureCarsAPI(message: IncomingMessage, aiConfig: any): Promise<any> {
    try {
      // Mock PureCars API call - replace with actual implementation
      const apiEndpoint = aiConfig.purecars_endpoint || process.env.PURECARS_API_ENDPOINT;
      const apiKey = aiConfig.purecars_api_key || process.env.PURECARS_API_KEY;

      if (!apiEndpoint || !apiKey) {
        throw new Error('PureCars API not configured');
      }

      // Simulate API delay
      const delay = aiConfig.response_delay_ms || 1000;
      await new Promise(resolve => setTimeout(resolve, delay));

      // Mock response - replace with actual fetch call
      const mockResponse = {
        success: true,
        response: `Thank you for your message about ${message.content}. I'm Rylie, your virtual assistant. How can I help you find the perfect vehicle today?`,
        confidence: 0.95,
        intent: 'general_inquiry',
        entities: {}
      };

      logger.info('PureCars API called', {
        dealershipId: message.dealershipId,
        success: mockResponse.success
      });

      return mockResponse;

    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error('PureCars API error', { error: err.message });
      return { success: false, error: err.message };
    }
  }

  private async queueForAgent(
    message: IncomingMessage, 
    conversationId: number, 
    config: any
  ): Promise<ConversationResponse> {
    try {
      // Add to agent queue
      const queuePosition = await this.addToAgentQueue(conversationId, message.dealershipId);
      const estimatedWaitTime = queuePosition * 3; // 3 minutes per position estimate

      // Send queue message
      const queueMessage = config.agentConfig.templates.queue_message || 
        `You're in queue position ${queuePosition}. Estimated wait time is ${estimatedWaitTime} minutes.`;
      
      await this.saveMessage(conversationId, queueMessage, false);

      // Create lead for tracking
      if (config.leadRouting.auto_create_leads) {
        await this.createOrUpdateLead(message, conversationId);
      }

      return {
        success: true,
        conversationId,
        response: queueMessage,
        action: 'agent_queue',
        estimatedWaitTime
      };

    } catch (error) {
      logger.error('Error queuing for agent', { error, conversationId });
      throw error;
    }
  }

  private async assignToAgent(
    dealershipId: number, 
    conversationId: number, 
    config: any
  ): Promise<number | null> {
    try {
      const strategy = config.leadRouting.lead_assignment_strategy || 'round_robin';
      
      // Get available agents
      const availableAgents = await this.getAvailableAgents(dealershipId);
      
      if (availableAgents.length === 0) {
        return null;
      }

      let selectedAgent: number;

      switch (strategy) {
        case 'round_robin':
          selectedAgent = await this.getRoundRobinAgent(dealershipId, availableAgents);
          break;
        case 'least_busy':
          selectedAgent = await this.getLeastBusyAgent(availableAgents);
          break;
        case 'skill_based':
          selectedAgent = await this.getSkillBasedAgent(availableAgents, conversationId);
          break;
        default:
          selectedAgent = availableAgents[0];
      }

      // Assign conversation to agent
      await this.assignConversationToAgent(conversationId, selectedAgent);
      
      return selectedAgent;

    } catch (error) {
      logger.error('Error assigning to agent', { error, dealershipId });
      return null;
    }
  }

  // Helper methods for conversation management

  private async findOrCreateConversation(message: IncomingMessage): Promise<number> {
    try {
      // Try to find existing conversation
      if (message.conversationId) {
        const exists = await this.conversationExists(message.conversationId);
        if (exists) {
          await this.saveMessage(message.conversationId, message.content, true);
          return message.conversationId;
        }
      }

      // Create new conversation
      const conversationId = await this.createConversation(message);
      await this.saveMessage(conversationId, message.content, true);
      
      return conversationId;

    } catch (error) {
      logger.error('Error finding/creating conversation', { error });
      throw error;
    }
  }

  private async createConversation(message: IncomingMessage): Promise<number> {
    try {
      const result = await db.execute(sql`
        INSERT INTO conversations (
          dealership_id,
          customer_name,
          customer_email,
          customer_phone,
          status,
          source,
          created_at,
          updated_at
        ) VALUES (
          ${message.dealershipId},
          ${message.customerName || ''},
          ${message.customerEmail || ''},
          ${message.customerPhone || ''},
          'active',
          ${message.source},
          ${new Date().toISOString()},
          ${new Date().toISOString()}
        )
        RETURNING id
      `);

      return (result.rows[0] as any).id;
    } catch (error) {
      logger.error('Error creating conversation', { error });
      throw error;
    }
  }

  private async saveMessage(
    conversationId: number, 
    content: string, 
    isFromCustomer: boolean
  ): Promise<void> {
    try {
      await db.execute(sql`
        INSERT INTO messages (
          conversation_id,
          content,
          is_from_customer,
          created_at,
          metadata
        ) VALUES (
          ${conversationId},
          ${content},
          ${isFromCustomer},
          ${new Date().toISOString()},
          '{}'
        )
      `);

      // Update conversation timestamp
      await db.execute(sql`
        UPDATE conversations 
        SET updated_at = ${new Date().toISOString()}
        WHERE id = ${conversationId}
      `);

    } catch (error) {
      logger.error('Error saving message', { error, conversationId });
      throw error;
    }
  }

  private async createOrUpdateLead(
    message: IncomingMessage, 
    conversationId: number
  ): Promise<void> {
    try {
      await this.leadService.createLead({
        dealershipId: message.dealershipId,
        conversationId,
        source: message.source,
        customerName: message.customerName,
        customerEmail: message.customerEmail,
        customerPhone: message.customerPhone,
        initialData: {
          firstMessage: message.content,
          channel: message.channel
        }
      });
    } catch (error) {
      logger.error('Error creating lead', { error, conversationId });
      // Don't throw - lead creation failure shouldn't break conversation flow
    }
  }

  // Queue and agent management methods
  private async addToAgentQueue(conversationId: number, dealershipId: number): Promise<number> {
    try {
      const result = await db.execute(sql`
        INSERT INTO agent_queue (
          conversation_id,
          dealership_id,
          queued_at,
          priority
        ) VALUES (
          ${conversationId},
          ${dealershipId},
          ${new Date().toISOString()},
          'normal'
        )
        RETURNING (
          SELECT COUNT(*) FROM agent_queue 
          WHERE dealership_id = ${dealershipId} 
          AND queued_at <= ${new Date().toISOString()}
        ) as position
      `);

      return (result.rows[0] as any).position || 1;
    } catch (error) {
      logger.error('Error adding to queue', { error, conversationId });
      return 1;
    }
  }

  private async getAvailableAgents(dealershipId: number): Promise<number[]> {
    try {
      const result = await db.execute(sql`
        SELECT id FROM users 
        WHERE dealership_id = ${dealershipId}
        AND role IN ('dealership_admin', 'manager', 'user')
        AND is_verified = true
        ORDER BY last_login DESC
      `);

      return result.rows.map((row: any) => row.id);
    } catch (error) {
      logger.error('Error getting available agents', { error, dealershipId });
      return [];
    }
  }

  // Additional helper methods would go here...
  private async conversationExists(conversationId: number): Promise<boolean> {
    const result = await db.execute(sql`SELECT 1 FROM conversations WHERE id = ${conversationId}`);
    return result.rows.length > 0;
  }

  private async getRoundRobinAgent(dealershipId: number, agents: number[]): Promise<number> {
    // Simple round-robin implementation
    return agents[Date.now() % agents.length];
  }

  private async getLeastBusyAgent(agents: number[]): Promise<number> {
    // Return first agent for now - could implement actual busy checking
    return agents[0];
  }

  private async getSkillBasedAgent(agents: number[], conversationId: number): Promise<number> {
    // Return first agent for now - could implement skill matching
    return agents[0];
  }

  private async assignConversationToAgent(conversationId: number, agentId: number): Promise<void> {
    await db.execute(sql`
      UPDATE conversations 
      SET assigned_to = ${agentId} 
      WHERE id = ${conversationId}
    `);
  }

  private async notifyAgent(agentId: number, conversationId: number, message: IncomingMessage): Promise<void> {
    // Notify agent via WebSocket if connected
    // Implementation would depend on your WebSocket setup
    logger.info('Agent notified of new conversation', { agentId, conversationId });
  }
}

export default ConversationRouter;