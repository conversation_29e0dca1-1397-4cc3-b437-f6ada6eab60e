# Code Cleanup Analysis

## Files to Remove (Redundant/Outdated)

### Server Root Level - Obsolete Files
- `auth-server.js` - Old JavaScript version, replaced by TypeScript
- `auth-server.ts` - Duplicate auth implementation 
- `auth-simplified.ts` - Experimental/test file
- `auth-solution.js` - Old JavaScript solution
- `auth.ts` - Duplicate auth service
- `direct-access-server.js` - Experimental bypass file
- `index-auth.ts` - Old auth-specific server entry
- `index-fixed.js` - Temporary fix file
- `logger.ts` - Replaced by utils/logger.ts
- `replitAuth.ts` - Platform-specific auth, not needed
- `simplified-server.js` - Old JavaScript version
- `simplified-server.ts` - Experimental simplified server
- `storage-modifications.ts` - Legacy storage file
- `storage.ts` - Old storage implementation

### Middleware Duplicates
- `middleware/auth-bypass.ts` - Experimental bypass
- `middleware/auth.ts` - Duplicate auth middleware (use authentication.ts)
- `middleware/tiered-rate-limit.ts` - Import syntax error, replace with rate-limit.ts

### Services Duplicates/Legacy
- `services/auth.ts` - Duplicate auth service (use magic-link-auth.ts)
- `services/email.ts` - Use email-service.ts instead
- `services/handover.ts` - Use handover-service.ts instead
- `services/abtest-openai-integration.ts` - Legacy A/B test implementation
- `services/cache-service.ts` vs `services/redis-cache-service.ts` - Circular dependency

### Standalone/Test Routes (Move to proper location)
- `routes/optimized-api.ts` - Merge with optimized-routes.ts
- `routes/performance-routes.ts` - Merge with monitoring-routes.ts
- `standalone-routes/` - Move to appropriate locations

### Database Migrations in Wrong Location
- `server/db/migrations/` - Should be in root migrations/ directory

### Root Level Cleanup
- `auth-server.js` - Old auth server
- `dashboard-entry.js` - Legacy dashboard
- `direct-dashboard.html` - Old HTML file
- `login-test.html` - Test file
- `real-dashboard-bypass.js` - Bypass file
- `simple-dashboard-server.js` - Legacy server
- `simple-prompt-test.js` - Use TypeScript version
- `standalone-dashboard.html` - Legacy HTML
- `test-*.js` files - Use TypeScript test files

### Scripts Cleanup
- Multiple duplicate test scripts
- Old shell scripts that don't work

### Client Duplicates
- Multiple `useAuth.ts` files in different locations
- Duplicate component files
- Multiple login.tsx files

## Directory Structure Issues

### Current Problems:
1. **Multiple auth implementations** scattered across directories
2. **Duplicate schemas** in server/shared/ and root shared/
3. **Test files** mixed with source code
4. **Legacy JavaScript** files alongside TypeScript
5. **Inconsistent import patterns**

### Proposed New Structure:
```
/server
  /core           # Core business logic
    /auth         # Authentication services
    /channels     # Channel routing & handlers  
    /agents       # Agent dashboard services
    /templates    # Prompt template system
  /api            # All route handlers
    /v1           # Versioned API routes
  /infrastructure # Database, cache, etc.
    /database     # DB connection & migrations
    /cache        # Cache services
    /monitoring   # Monitoring & metrics
  /utils          # Shared utilities
  /types          # TypeScript type definitions

/shared           # Shared between client/server
  /types          # TypeScript interfaces
  /schemas        # Validation schemas
  /constants      # Shared constants

/test             # All test files
  /unit
  /integration
  /e2e
```

## Circular Dependencies Identified

1. **Cache Services**: cache-service.ts ↔ redis-cache-service.ts
2. **Auth Services**: Multiple auth files referencing each other
3. **Schema Dependencies**: server/shared ↔ root shared

## Import Pattern Standardization

### Current: Mixed relative/absolute
### Proposed: Absolute imports from src root with path mapping