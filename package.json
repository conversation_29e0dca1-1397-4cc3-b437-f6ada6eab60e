{"name": "rest-express", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"dev": "NODE_ENV=development tsx server/index.ts", "build": "vite build && esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist", "start": "NODE_ENV=production node dist/index.js", "check": "tsc", "db:push": "drizzle-kit push", "migrate": "tsx server/cli/migrate.ts", "migrate:rollback": "tsx server/cli/migrate.ts rollback", "migrate:status": "tsx server/cli/migrate.ts status", "migrate:validate": "tsx server/cli/migrate.ts validate", "test": "jest", "test:adf": "jest test/adf", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@jridgewell/trace-mapping": "^0.3.25", "@neondatabase/serverless": "^0.10.4", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-aspect-ratio": "^1.1.3", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.7", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-hover-card": "^1.1.7", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-menubar": "^1.1.7", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.0", "@sendgrid/mail": "^8.1.5", "@tanstack/react-query": "^5.76.1", "@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.18", "@types/csurf": "^1.11.5", "@types/jest": "^29.5.14", "@types/js-yaml": "^4.0.9", "@types/memoizee": "^0.4.12", "@types/nodemailer": "^6.4.17", "@types/supertest": "^6.0.3", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@types/twilio": "^3.19.2", "@types/uuid": "^10.0.0", "bcrypt": "^6.0.0", "bull": "^4.16.5", "chalk": "^5.4.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "connect-pg-simple": "^10.0.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "csurf": "^1.11.0", "csv-parser": "^3.2.0", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.39.3", "drizzle-zod": "^0.7.1", "embla-carousel-react": "^8.6.0", "express": "^4.21.2", "express-prom-bundle": "^8.0.0", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "express-validator": "^7.2.1", "fast-xml-parser": "^5.2.3", "framer-motion": "^11.13.1", "imap": "^0.8.19", "input-otp": "^1.4.2", "ioredis": "^5.6.1", "jest": "^29.7.0", "js-yaml": "^4.1.0", "lucide-react": "^0.453.0", "mailparser": "^3.7.3", "memoizee": "^0.4.17", "memorystore": "^1.6.7", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "node-schedule": "^2.1.1", "nodemailer": "^7.0.3", "openai": "^4.103.0", "pg": "^8.16.0", "postgres": "^3.4.7", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.56.4", "recharts": "^2.15.3", "supertest": "^7.1.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "swr": "^2.3.3", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "ts-jest": "^29.3.4", "tw-animate-css": "^1.2.5", "twilio": "^5.6.1", "uuid": "^11.1.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "wouter": "^3.7.0", "ws": "^8.18.2", "zod": "^3.25.20", "zod-to-json-schema": "^3.24.5", "zod-validation-error": "^3.4.0"}, "devDependencies": {"@replit/vite-plugin-cartographer": "^0.2.4", "@replit/vite-plugin-runtime-error-modal": "^0.0.3", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.1.3", "@types/connect-pg-simple": "^7.0.3", "@types/express": "4.17.21", "@types/express-session": "^1.18.0", "@types/imap": "^0.8.42", "@types/node": "20.16.11", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@types/ws": "^8.18.1", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "drizzle-kit": "^0.30.4", "esbuild": "^0.25.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.17", "tsx": "^4.19.1", "typescript": "5.6.3", "vite": "^5.4.14"}, "optionalDependencies": {"bufferutil": "^4.0.8"}}