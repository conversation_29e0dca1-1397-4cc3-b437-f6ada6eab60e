/**
 * Simple login application for testing the RylieAI system
 */
import express from 'express';
import { Pool } from 'pg';
import cors from 'cors';
import path from 'path';
import { fileURLToPath } from 'url';

// Setup __dirname equivalent for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create Express app
const app = express();
const port = 3000;

// Database connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
});

// Middleware
app.use(express.json());
app.use(express.static('.'));
app.use(cors());

// API endpoints
app.post('/api/login', async (req, res) => {
  const { username, password } = req.body;
  
  try {
    // Query database for user
    const result = await pool.query(
      'SELECT id, username, email, name, role, dealership_id FROM users WHERE username = $1',
      [username]
    );
    
    const user = result.rows[0];
    
    // Check if user exists and password matches
    // For testing purposes, we're checking for hardcoded 'password123'
    if (!user || password !== 'password123') {
      return res.status(401).json({ error: 'Invalid username or password' });
    }
    
    // Return user data on successful login
    res.json(user);
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Server error during login' });
  }
});

app.get('/api/dealerships', async (req, res) => {
  try {
    const result = await pool.query('SELECT * FROM dealerships');
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching dealerships:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

app.get('/api/users', async (req, res) => {
  try {
    const result = await pool.query('SELECT id, username, email, name, role, dealership_id FROM users');
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

// Start server
app.listen(port, '0.0.0.0', () => {
  console.log(`Server running at http://localhost:${port}`);
  
  // Test database connection
  pool.query('SELECT NOW()', (err, result) => {
    if (err) {
      console.error('Database connection error:', err);
    } else {
      console.log('Database connected successfully:', result.rows[0].now);
    }
  });
});