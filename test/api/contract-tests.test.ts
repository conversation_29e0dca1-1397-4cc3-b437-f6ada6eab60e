import { describe, test, expect, beforeAll, afterAll } from '@jest/globals';
import request from 'supertest';
import { app } from '../../server/index';
import { generateTestApiKey, createTestLead, createTestConversation } from '../helpers/test-data';

// Test data for contract validation
const validInboundLead = {
  customer: {
    firstName: "<PERSON>",
    lastName: "<PERSON>", 
    fullName: "<PERSON>",
    email: "<EMAIL>",
    phone: "******-123-4567",
    city: "Springfield",
    state: "IL",
    zipCode: "62701"
  },
  vehicleInterest: {
    year: 2024,
    make: "Honda",
    model: "Accord",
    condition: "new",
    maxPrice: 35000
  },
  lead: {
    requestType: "Purchase",
    description: "Looking for a reliable sedan",
    source: "website_form",
    priority: "medium"
  },
  attribution: {
    source: "google",
    medium: "cpc",
    campaign: "honda_accord_2024"
  }
};

const validReplyMessage = {
  conversationId: "f47ac10b-58cc-4372-a567-0e02b2c3d479",
  content: "Thank you for your interest in the 2024 Honda Accord.",
  contentType: "text",
  sender: "ai",
  senderName: "Rylie AI"
};

const validHandoverRequest = {
  conversationId: "f47ac10b-58cc-4372-a567-0e02b2c3d479",
  reason: "pricing_negotiation",
  description: "Customer ready to negotiate pricing",
  urgency: "high"
};

describe('API Contract Tests', () => {
  let apiKey: string;

  beforeAll(async () => {
    apiKey = await generateTestApiKey();
  });

  describe('POST /api/v1/inbound - Lead Creation', () => {
    test('should accept valid inbound lead payload', async () => {
      const response = await request(app)
        .post('/api/v1/inbound')
        .set('Authorization', `Bearer ${apiKey}`)
        .set('Content-Type', 'application/json')
        .send(validInboundLead);

      expect(response.status).toBe(201);
      expect(response.body).toMatchObject({
        success: true,
        data: {
          leadId: expect.stringMatching(/^[0-9a-f-]{36}$/),
          customerId: expect.stringMatching(/^[0-9a-f-]{36}$/),
          conversationId: expect.stringMatching(/^[0-9a-f-]{36}$/),
          isExistingCustomer: expect.any(Boolean)
        },
        message: expect.any(String)
      });
    });

    test('should reject payload with missing required customer fields', async () => {
      const invalidPayload = {
        ...validInboundLead,
        customer: {
          firstName: "John"
          // Missing required fullName
        }
      };

      const response = await request(app)
        .post('/api/v1/inbound')
        .set('Authorization', `Bearer ${apiKey}`)
        .set('Content-Type', 'application/json')
        .send(invalidPayload);

      expect(response.status).toBe(400);
      expect(response.body).toMatchObject({
        success: false,
        error: 'Validation failed',
        details: expect.arrayContaining([
          expect.objectContaining({
            field: 'customer.fullName',
            message: expect.any(String),
            code: expect.any(String)
          })
        ])
      });
    });

    test('should reject payload with invalid email format', async () => {
      const invalidPayload = {
        ...validInboundLead,
        customer: {
          ...validInboundLead.customer,
          email: "invalid-email"
        }
      };

      const response = await request(app)
        .post('/api/v1/inbound')
        .set('Authorization', `Bearer ${apiKey}`)
        .set('Content-Type', 'application/json')
        .send(invalidPayload);

      expect(response.status).toBe(400);
      expect(response.body.details).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            field: 'customer.email',
            code: 'invalid_string'
          })
        ])
      );
    });

    test('should reject payload with invalid lead source', async () => {
      const invalidPayload = {
        ...validInboundLead,
        lead: {
          ...validInboundLead.lead,
          source: "invalid_source"
        }
      };

      const response = await request(app)
        .post('/api/v1/inbound')
        .set('Authorization', `Bearer ${apiKey}`)
        .set('Content-Type', 'application/json')
        .send(invalidPayload);

      expect(response.status).toBe(400);
      expect(response.body.details).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            field: 'lead.source',
            code: 'invalid_enum_value'
          })
        ])
      );
    });

    test('should reject payload with invalid vehicle year', async () => {
      const invalidPayload = {
        ...validInboundLead,
        vehicleInterest: {
          ...validInboundLead.vehicleInterest,
          year: 1800 // Too old
        }
      };

      const response = await request(app)
        .post('/api/v1/inbound')
        .set('Authorization', `Bearer ${apiKey}`)
        .set('Content-Type', 'application/json')
        .send(invalidPayload);

      expect(response.status).toBe(400);
      expect(response.body.details).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            field: 'vehicleInterest.year',
            code: 'too_small'
          })
        ])
      );
    });

    test('should reject payload exceeding size limit', async () => {
      const oversizedPayload = {
        ...validInboundLead,
        customFields: {
          largeData: 'x'.repeat(2 * 1024 * 1024) // 2MB of data
        }
      };

      const response = await request(app)
        .post('/api/v1/inbound')
        .set('Authorization', `Bearer ${apiKey}`)
        .set('Content-Type', 'application/json')
        .send(oversizedPayload);

      expect(response.status).toBe(413);
      expect(response.body).toMatchObject({
        success: false,
        error: 'Request body too large'
      });
    });

    test('should reject payload with wrong content type', async () => {
      const response = await request(app)
        .post('/api/v1/inbound')
        .set('Authorization', `Bearer ${apiKey}`)
        .set('Content-Type', 'text/plain')
        .send('invalid content type');

      expect(response.status).toBe(415);
      expect(response.body).toMatchObject({
        success: false,
        error: 'Unsupported Media Type'
      });
    });
  });

  describe('POST /api/v1/reply - Message Reply', () => {
    test('should accept valid reply message payload', async () => {
      const conversation = await createTestConversation();
      const replyPayload = {
        ...validReplyMessage,
        conversationId: conversation.id
      };

      const response = await request(app)
        .post('/api/v1/reply')
        .set('Authorization', `Bearer ${apiKey}`)
        .set('Content-Type', 'application/json')
        .send(replyPayload);

      expect(response.status).toBe(201);
      expect(response.body).toMatchObject({
        success: true,
        data: {
          messageId: expect.stringMatching(/^[0-9a-f-]{36}$/),
          conversationId: conversation.id,
          timestamp: expect.any(String)
        }
      });
    });

    test('should reject reply with invalid UUID conversation ID', async () => {
      const invalidPayload = {
        ...validReplyMessage,
        conversationId: "invalid-uuid"
      };

      const response = await request(app)
        .post('/api/v1/reply')
        .set('Authorization', `Bearer ${apiKey}`)
        .set('Content-Type', 'application/json')
        .send(invalidPayload);

      expect(response.status).toBe(400);
      expect(response.body.details).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            field: 'conversationId',
            code: 'invalid_string'
          })
        ])
      );
    });

    test('should reject reply with empty content', async () => {
      const invalidPayload = {
        ...validReplyMessage,
        content: ""
      };

      const response = await request(app)
        .post('/api/v1/reply')
        .set('Authorization', `Bearer ${apiKey}`)
        .set('Content-Type', 'application/json')
        .send(invalidPayload);

      expect(response.status).toBe(400);
      expect(response.body.details).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            field: 'content',
            code: 'too_small'
          })
        ])
      );
    });

    test('should reject reply with invalid sender type', async () => {
      const invalidPayload = {
        ...validReplyMessage,
        sender: "invalid_sender"
      };

      const response = await request(app)
        .post('/api/v1/reply')
        .set('Authorization', `Bearer ${apiKey}`)
        .set('Content-Type', 'application/json')
        .send(invalidPayload);

      expect(response.status).toBe(400);
      expect(response.body.details).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            field: 'sender',
            code: 'invalid_enum_value'
          })
        ])
      );
    });
  });

  describe('POST /api/v1/handover - Handover Request', () => {
    test('should accept valid handover request payload', async () => {
      const conversation = await createTestConversation();
      const handoverPayload = {
        ...validHandoverRequest,
        conversationId: conversation.id
      };

      const response = await request(app)
        .post('/api/v1/handover')
        .set('Authorization', `Bearer ${apiKey}`)
        .set('Content-Type', 'application/json')
        .send(handoverPayload);

      expect(response.status).toBe(201);
      expect(response.body).toMatchObject({
        success: true,
        data: {
          handoverId: expect.stringMatching(/^[0-9a-f-]{36}$/),
          conversationId: conversation.id,
          status: expect.any(String),
          estimatedResponseTime: expect.any(String)
        }
      });
    });

    test('should reject handover with invalid reason', async () => {
      const invalidPayload = {
        ...validHandoverRequest,
        reason: "invalid_reason"
      };

      const response = await request(app)
        .post('/api/v1/handover')
        .set('Authorization', `Bearer ${apiKey}`)
        .set('Content-Type', 'application/json')
        .send(invalidPayload);

      expect(response.status).toBe(400);
      expect(response.body.details).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            field: 'reason',
            code: 'invalid_enum_value'
          })
        ])
      );
    });

    test('should reject handover with missing description', async () => {
      const invalidPayload = {
        ...validHandoverRequest,
        description: undefined
      };

      const response = await request(app)
        .post('/api/v1/handover')
        .set('Authorization', `Bearer ${apiKey}`)
        .set('Content-Type', 'application/json')
        .send(invalidPayload);

      expect(response.status).toBe(400);
      expect(response.body.details).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            field: 'description',
            code: 'invalid_type'
          })
        ])
      );
    });
  });

  describe('GET /api/v1/leads - Lead List', () => {
    test('should accept valid query parameters', async () => {
      const response = await request(app)
        .get('/api/v1/leads')
        .query({
          limit: '25',
          offset: '0',
          status: 'new',
          source: 'website_form'
        })
        .set('Authorization', `Bearer ${apiKey}`);

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        success: true,
        data: expect.any(Array),
        pagination: {
          limit: 25,
          offset: 0,
          total: expect.any(Number)
        }
      });
    });

    test('should reject invalid limit parameter', async () => {
      const response = await request(app)
        .get('/api/v1/leads')
        .query({ limit: '150' }) // Exceeds maximum
        .set('Authorization', `Bearer ${apiKey}`);

      expect(response.status).toBe(400);
      expect(response.body.details).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            field: 'limit',
            code: 'too_big'
          })
        ])
      );
    });

    test('should reject invalid status parameter', async () => {
      const response = await request(app)
        .get('/api/v1/leads')
        .query({ status: 'invalid_status' })
        .set('Authorization', `Bearer ${apiKey}`);

      expect(response.status).toBe(400);
      expect(response.body.details).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            field: 'status',
            code: 'invalid_enum_value'
          })
        ])
      );
    });
  });

  describe('GET /api/v1/leads/:leadId - Lead Detail', () => {
    test('should accept valid UUID parameter', async () => {
      const lead = await createTestLead();
      
      const response = await request(app)
        .get(`/api/v1/leads/${lead.id}`)
        .set('Authorization', `Bearer ${apiKey}`);

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        success: true,
        data: {
          id: lead.id,
          leadNumber: expect.any(String),
          status: expect.any(String)
        }
      });
    });

    test('should reject invalid UUID parameter', async () => {
      const response = await request(app)
        .get('/api/v1/leads/invalid-uuid')
        .set('Authorization', `Bearer ${apiKey}`);

      expect(response.status).toBe(400);
      expect(response.body.details).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            field: 'leadId',
            message: expect.stringContaining('Invalid leadId format')
          })
        ])
      );
    });
  });

  describe('Authentication Contract Tests', () => {
    test('should reject requests without API key', async () => {
      const response = await request(app)
        .post('/api/v1/inbound')
        .set('Content-Type', 'application/json')
        .send(validInboundLead);

      expect(response.status).toBe(401);
      expect(response.body).toMatchObject({
        success: false,
        error: 'Missing Authorization header'
      });
    });

    test('should reject requests with invalid API key', async () => {
      const response = await request(app)
        .post('/api/v1/inbound')
        .set('Authorization', 'Bearer invalid-api-key')
        .set('Content-Type', 'application/json')
        .send(validInboundLead);

      expect(response.status).toBe(401);
      expect(response.body).toMatchObject({
        success: false,
        error: 'Invalid API key'
      });
    });

    test('should accept different authorization header formats', async () => {
      // Test Bearer format
      let response = await request(app)
        .get('/api/v1/leads')
        .set('Authorization', `Bearer ${apiKey}`);
      
      expect(response.status).not.toBe(401);

      // Test ApiKey format
      response = await request(app)
        .get('/api/v1/leads')
        .set('Authorization', `ApiKey ${apiKey}`);
      
      expect(response.status).not.toBe(401);
    });
  });

  describe('Response Contract Tests', () => {
    test('all success responses should have consistent structure', async () => {
      const response = await request(app)
        .get('/api/v1/leads')
        .set('Authorization', `Bearer ${apiKey}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      
      if (response.body.message) {
        expect(typeof response.body.message).toBe('string');
      }
      
      if (response.body.pagination) {
        expect(response.body.pagination).toMatchObject({
          limit: expect.any(Number),
          offset: expect.any(Number),
          total: expect.any(Number)
        });
      }
    });

    test('all error responses should have consistent structure', async () => {
      const response = await request(app)
        .post('/api/v1/inbound')
        .set('Authorization', `Bearer ${apiKey}`)
        .set('Content-Type', 'application/json')
        .send({ invalid: 'payload' });

      expect(response.status).toBe(400);
      expect(response.body).toMatchObject({
        success: false,
        error: expect.any(String),
        details: expect.any(Array),
        timestamp: expect.any(String)
      });
    });
  });
});