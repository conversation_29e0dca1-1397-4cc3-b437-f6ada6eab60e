import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { AdfService } from '../../server/services/adf-service';
import { validAdfXmlSamples, invalidAdfXmlSamples } from './sample-adf-data';

// Mock all external dependencies
jest.mock('../../shared/db');
jest.mock('../../server/services/dealership-finder');
jest.mock('imap');
jest.mock('mailparser');

describe('ADF Integration Tests', () => {
  let adfService: AdfService;
  let mockImapConfig: any;

  beforeEach(() => {
    mockImapConfig = {
      user: '<EMAIL>',
      password: 'password',
      host: 'imap.example.com',
      port: 993,
      tls: true
    };

    adfService = new AdfService(mockImapConfig);
    jest.clearAllMocks();
  });

  afterEach(() => {
    adfService.stop();
    jest.resetAllMocks();
  });

  describe('End-to-End Email Processing', () => {
    it('should process email with valid ADF content end-to-end', async () => {
      // Mock successful dealership lookup
      const { findDealershipByEmail } = require('../../server/services/dealership-finder');
      findDealershipByEmail.mockResolvedValue({ id: 1, name: 'Test Dealer' });

      // Mock database operations
      const { db } = require('../../shared/db');
      db.select.mockReturnValue({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockResolvedValue([]) // No duplicates
        })
      });

      db.insert.mockReturnValue({
        values: jest.fn().mockReturnValue({
          returning: jest.fn().mockResolvedValue([{ id: 123, deduplicationHash: 'test-hash' }])
        })
      });

      // Start the service
      await adfService.start();

      // Simulate email processing
      const result = await adfService.processManualAdf(validAdfXmlSamples.comprehensive);

      expect(result.success).toBe(true);
      expect(result.leadId).toBe(123);
      expect(result.errors).toHaveLength(0);
    });

    it('should handle errors in the processing pipeline', async () => {
      const errorEvents: any[] = [];

      adfService.on('processingError', (error) => errorEvents.push(error));

      await adfService.start();

      // Simulate processing invalid ADF
      const result = await adfService.processManualAdf(invalidAdfXmlSamples.malformedXml);

      expect(result.success).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('Service Lifecycle', () => {
    it('should start all components successfully', async () => {
      const startPromise = adfService.start();
      
      await expect(startPromise).resolves.toBeUndefined();
      expect(adfService.isRunning()).toBe(true);
    });

    it('should stop all components cleanly', async () => {
      await adfService.start();
      expect(adfService.isRunning()).toBe(true);

      adfService.stop();
      expect(adfService.isRunning()).toBe(false);
    });
  });
});