import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { 
  ChannelRoutingService, 
  PriorityChannelStrategy,
  UrgencyChannelStrategy,
  ChannelMessage,
  CommunicationChannel 
} from '../../server/services/channel-routing-service';

// Mock database
vi.mock('../../server/db', () => ({
  default: {
    execute: vi.fn()
  }
}));

// Mock logger
vi.mock('../../server/utils/logger', () => ({
  default: {
    info: vi.fn(),
    error: vi.fn(),
    warn: vi.fn()
  }
}));

describe('ChannelRoutingService', () => {
  let routingService: ChannelRoutingService;
  let mockDbExecute: any;

  beforeEach(() => {
    vi.clearAllMocks();
    
    const db = require('../../server/db').default;
    mockDbExecute = vi.mocked(db.execute);
    
    routingService = new ChannelRoutingService();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('routeMessage', () => {
    it('should route message to preferred customer channel', async () => {
      const testMessage: ChannelMessage = {
        customerId: 1,
        dealershipId: 1,
        content: 'Test message',
        urgencyLevel: 'normal'
      };

      // Mock customer preferences
      mockDbExecute
        .mockResolvedValueOnce({
          rows: [{
            customer_id: 1,
            dealership_id: 1,
            channel: 'email',
            preference_type: 'preferred',
            priority: 1
          }]
        })
        // Mock dealership rules
        .mockResolvedValueOnce({
          rows: [{
            dealership_id: 1,
            channel: 'sms',
            priority: 1,
            urgency_level: 'normal'
          }]
        })
        // Mock available channels
        .mockResolvedValueOnce({
          rows: [
            { channel: 'email' },
            { channel: 'sms' },
            { channel: 'web_chat' }
          ]
        })
        // Mock fallback channels
        .mockResolvedValueOnce({ rows: [] })
        // Mock delivery attempt creation
        .mockResolvedValueOnce({
          rows: [{ id: 'attempt-123' }]
        });

      const result = await routingService.routeMessage(testMessage);

      expect(result.selectedChannel).toBe('email');
      expect(result.reason).toBe('user_preference');
      expect(result.deliveryAttemptId).toBe('attempt-123');
    });

    it('should fall back to dealership rules when no customer preference', async () => {
      const testMessage: ChannelMessage = {
        customerId: 1,
        dealershipId: 1,
        content: 'Test message',
        urgencyLevel: 'high',
        leadSource: 'website'
      };

      // Mock empty customer preferences
      mockDbExecute
        .mockResolvedValueOnce({ rows: [] })
        // Mock dealership rules
        .mockResolvedValueOnce({
          rows: [{
            dealership_id: 1,
            lead_source: 'website',
            channel: 'sms',
            priority: 1,
            urgency_level: 'high'
          }]
        })
        // Mock available channels
        .mockResolvedValueOnce({
          rows: [{ channel: 'sms' }, { channel: 'email' }]
        })
        // Mock fallback channels
        .mockResolvedValueOnce({ rows: [] })
        // Mock delivery attempt creation
        .mockResolvedValueOnce({
          rows: [{ id: 'attempt-456' }]
        });

      const result = await routingService.routeMessage(testMessage);

      expect(result.selectedChannel).toBe('sms');
      expect(result.reason).toBe('urgency_level');
    });

    it('should handle no available channels gracefully', async () => {
      const testMessage: ChannelMessage = {
        customerId: 1,
        dealershipId: 1,
        content: 'Test message'
      };

      // Mock empty responses
      mockDbExecute
        .mockResolvedValueOnce({ rows: [] }) // customer preferences
        .mockResolvedValueOnce({ rows: [] }) // dealership rules
        .mockResolvedValueOnce({ rows: [] }); // available channels

      await expect(routingService.routeMessage(testMessage))
        .rejects.toThrow('No available channels for message delivery');
    });
  });

  describe('updateDeliveryStatus', () => {
    it('should update delivery status successfully', async () => {
      mockDbExecute.mockResolvedValueOnce({ rows: [] });

      await expect(
        routingService.updateDeliveryStatus('attempt-123', 'delivered', { test: 'metadata' })
      ).resolves.not.toThrow();

      expect(mockDbExecute).toHaveBeenCalledWith(
        expect.objectContaining({
          sql: expect.stringContaining('UPDATE message_delivery_attempts')
        })
      );
    });
  });

  describe('handleDeliveryFailure', () => {
    it('should trigger fallback when delivery fails', async () => {
      // Mock failed attempt details
      mockDbExecute
        .mockResolvedValueOnce({ rows: [] }) // Update failed attempt
        .mockResolvedValueOnce({
          rows: [{
            message_id: 'msg-123',
            conversation_id: 1,
            customer_id: 1,
            dealership_id: 1,
            channel: 'email',
            attempt_number: 1
          }]
        })
        // Mock fallback channels
        .mockResolvedValueOnce({
          rows: [{ fallback_channel: 'sms' }]
        })
        // Mock available channels
        .mockResolvedValueOnce({
          rows: [{ channel: 'sms' }]
        })
        // Mock new delivery attempt creation
        .mockResolvedValueOnce({
          rows: [{ id: 'fallback-attempt-789' }]
        });

      const result = await routingService.handleDeliveryFailure(
        'attempt-123',
        'Email delivery failed',
        'SMTP_ERROR'
      );

      expect(result).not.toBeNull();
      expect(result?.selectedChannel).toBe('sms');
      expect(result?.reason).toBe('fallback');
    });

    it('should return null when no fallback channels available', async () => {
      mockDbExecute
        .mockResolvedValueOnce({ rows: [] }) // Update failed attempt
        .mockResolvedValueOnce({
          rows: [{
            message_id: 'msg-123',
            customer_id: 1,
            dealership_id: 1,
            channel: 'email',
            attempt_number: 3 // Max attempts reached
          }]
        })
        .mockResolvedValueOnce({ rows: [] }); // No fallback channels

      const result = await routingService.handleDeliveryFailure(
        'attempt-123',
        'Email delivery failed'
      );

      expect(result).toBeNull();
    });
  });

  describe('getChannelPerformanceMetrics', () => {
    it('should return channel performance data', async () => {
      const mockMetrics = [
        {
          channel: 'email',
          total_attempts: 100,
          delivered_count: 95,
          opened_count: 60,
          clicked_count: 30,
          failed_count: 5,
          avg_delivery_minutes: 2.5,
          avg_response_minutes: 45.2
        },
        {
          channel: 'sms',
          total_attempts: 80,
          delivered_count: 78,
          opened_count: 0,
          clicked_count: 0,
          failed_count: 2,
          avg_delivery_minutes: 0.8,
          avg_response_minutes: 15.5
        }
      ];

      mockDbExecute.mockResolvedValueOnce({ rows: mockMetrics });

      const startDate = new Date('2023-01-01');
      const endDate = new Date('2023-01-31');
      
      const result = await routingService.getChannelPerformanceMetrics(1, startDate, endDate);

      expect(result).toEqual(mockMetrics);
      expect(mockDbExecute).toHaveBeenCalledWith(
        expect.objectContaining({
          sql: expect.stringContaining('FROM message_delivery_attempts')
        })
      );
    });
  });
});

describe('PriorityChannelStrategy', () => {
  let strategy: PriorityChannelStrategy;

  beforeEach(() => {
    strategy = new PriorityChannelStrategy();
  });

  it('should select preferred customer channel first', async () => {
    const message: ChannelMessage = {
      customerId: 1,
      dealershipId: 1,
      content: 'Test message'
    };

    const availableChannels: CommunicationChannel[] = ['email', 'sms', 'web_chat'];
    
    const customerPreferences = [
      {
        customerId: 1,
        dealership: 1,
        channel: 'sms' as CommunicationChannel,
        preferenceType: 'preferred' as const,
        priority: 1
      }
    ];

    const dealershipRules = [
      {
        dealershipId: 1,
        urgencyLevel: 'normal' as const,
        channel: 'email' as CommunicationChannel,
        priority: 1,
        maxAttempts: 3,
        retryDelayMinutes: 5,
        businessHoursOnly: false
      }
    ];

    const selectedChannel = await strategy.selectChannel(
      message,
      availableChannels,
      customerPreferences,
      dealershipRules
    );

    expect(selectedChannel).toBe('sms');
  });

  it('should fall back to dealership rules when no customer preferences', async () => {
    const message: ChannelMessage = {
      customerId: 1,
      dealershipId: 1,
      content: 'Test message',
      leadSource: 'website'
    };

    const availableChannels: CommunicationChannel[] = ['email', 'sms'];
    const customerPreferences: any[] = [];
    
    const dealershipRules = [
      {
        dealershipId: 1,
        leadSource: 'website',
        urgencyLevel: 'normal' as const,
        channel: 'email' as CommunicationChannel,
        priority: 1,
        maxAttempts: 3,
        retryDelayMinutes: 5,
        businessHoursOnly: false
      }
    ];

    const selectedChannel = await strategy.selectChannel(
      message,
      availableChannels,
      customerPreferences,
      dealershipRules
    );

    expect(selectedChannel).toBe('email');
  });

  it('should return first available channel as last resort', async () => {
    const message: ChannelMessage = {
      customerId: 1,
      dealershipId: 1,
      content: 'Test message'
    };

    const availableChannels: CommunicationChannel[] = ['web_chat'];
    const customerPreferences: any[] = [];
    const dealershipRules: any[] = [];

    const selectedChannel = await strategy.selectChannel(
      message,
      availableChannels,
      customerPreferences,
      dealershipRules
    );

    expect(selectedChannel).toBe('web_chat');
  });
});

describe('UrgencyChannelStrategy', () => {
  let strategy: UrgencyChannelStrategy;

  beforeEach(() => {
    strategy = new UrgencyChannelStrategy();
  });

  it('should prefer real-time channels for urgent messages', async () => {
    const message: ChannelMessage = {
      customerId: 1,
      dealershipId: 1,
      content: 'Urgent message',
      urgencyLevel: 'urgent'
    };

    const availableChannels: CommunicationChannel[] = ['email', 'sms', 'web_chat'];

    const selectedChannel = await strategy.selectChannel(
      message,
      availableChannels,
      [],
      []
    );

    expect(['sms', 'web_chat', 'phone']).toContain(selectedChannel);
  });

  it('should prefer SMS for high priority messages', async () => {
    const message: ChannelMessage = {
      customerId: 1,
      dealershipId: 1,
      content: 'High priority message',
      urgencyLevel: 'high'
    };

    const availableChannels: CommunicationChannel[] = ['email', 'sms'];

    const selectedChannel = await strategy.selectChannel(
      message,
      availableChannels,
      [],
      []
    );

    expect(selectedChannel).toBe('sms');
  });

  it('should use email for normal priority messages', async () => {
    const message: ChannelMessage = {
      customerId: 1,
      dealershipId: 1,
      content: 'Normal message',
      urgencyLevel: 'normal'
    };

    const availableChannels: CommunicationChannel[] = ['email', 'sms'];

    const selectedChannel = await strategy.selectChannel(
      message,
      availableChannels,
      [],
      []
    );

    expect(selectedChannel).toBe('email');
  });
});

describe('Channel Routing Integration Tests', () => {
  let routingService: ChannelRoutingService;

  beforeEach(() => {
    routingService = new ChannelRoutingService();
  });

  it('should handle complete routing workflow with fallbacks', async () => {
    const testMessage: ChannelMessage = {
      customerId: 1,
      dealershipId: 1,
      content: 'Integration test message',
      urgencyLevel: 'high'
    };

    // Mock the complete workflow
    mockDbExecute
      // Customer preferences (blocked email)
      .mockResolvedValueOnce({
        rows: [{
          customer_id: 1,
          dealership_id: 1,
          channel: 'email',
          preference_type: 'blocked',
          priority: 1
        }]
      })
      // Dealership rules (prefer SMS for high urgency)
      .mockResolvedValueOnce({
        rows: [{
          dealership_id: 1,
          channel: 'sms',
          priority: 1,
          urgency_level: 'high'
        }]
      })
      // Available channels
      .mockResolvedValueOnce({
        rows: [
          { channel: 'sms' },
          { channel: 'web_chat' }
        ]
      })
      // Fallback chains
      .mockResolvedValueOnce({
        rows: [{ fallback_channel: 'web_chat' }]
      })
      // Create delivery attempt
      .mockResolvedValueOnce({
        rows: [{ id: 'integration-test-123' }]
      });

    const result = await routingService.routeMessage(testMessage);

    expect(result.selectedChannel).toBe('sms');
    expect(result.fallbackChannels).toEqual(['web_chat']);
    expect(result.deliveryAttemptId).toBe('integration-test-123');
  });
});

describe('Channel Routing Error Handling', () => {
  let routingService: ChannelRoutingService;

  beforeEach(() => {
    routingService = new ChannelRoutingService();
  });

  it('should handle database errors gracefully', async () => {
    const testMessage: ChannelMessage = {
      customerId: 1,
      dealershipId: 1,
      content: 'Test message'
    };

    // Mock database error
    mockDbExecute.mockRejectedValueOnce(new Error('Database connection failed'));

    await expect(routingService.routeMessage(testMessage))
      .rejects.toThrow('Database connection failed');
  });

  it('should handle malformed data gracefully', async () => {
    const testMessage: ChannelMessage = {
      customerId: 1,
      dealershipId: 1,
      content: 'Test message'
    };

    // Mock malformed response
    mockDbExecute.mockResolvedValueOnce({ rows: null });

    await expect(routingService.routeMessage(testMessage))
      .rejects.toThrow();
  });
});