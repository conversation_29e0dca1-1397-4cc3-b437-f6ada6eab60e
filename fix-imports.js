#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Files that need import fixes
const filesToFix = [
  'server/services/optimized-queries.ts',
  'server/services/dealership-config.ts', 
  'server/services/conversation-router.ts',
  'server/services/messaging-service.ts',
  'server/services/lead-management.ts',
  'server/services/chat-server.ts',
  'server/routes/setup-routes.ts',
  'server/routes/monitoring-routes.ts',
  'server/services/dealership-config-service.ts',
  'server/services/chat-service.ts',
  'server/routes/cache-demo-routes.ts',
  'server/routes.ts',
  'server/routes/admin-user-routes.ts'
];

function fixImports() {
  console.log('🔧 Fixing database import issues...\n');

  filesToFix.forEach(filePath => {
    const fullPath = path.resolve(__dirname, filePath);
    
    if (!fs.existsSync(fullPath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return;
    }

    try {
      let content = fs.readFileSync(fullPath, 'utf8');
      
      // Fix the main db import
      const originalContent = content;
      content = content.replace(
        /import\s*{\s*db\s*}\s*from\s*['"`]\.\.\/db['"`];?/g,
        "import db from '../db';"
      );
      
      // Also handle cases where it might be a different relative path
      content = content.replace(
        /import\s*{\s*db\s*}\s*from\s*['"`]\.\.\/\.\.\/db['"`];?/g,
        "import db from '../../db';"
      );

      // Write back if changed
      if (content !== originalContent) {
        fs.writeFileSync(fullPath, content, 'utf8');
        console.log(`✅ Fixed imports in: ${filePath}`);
      } else {
        console.log(`ℹ️  No changes needed in: ${filePath}`);
      }
      
    } catch (error) {
      console.error(`❌ Error fixing ${filePath}:`, error.message);
    }
  });

  console.log('\n🎉 Import fix process completed!');
  console.log('\nNext steps:');
  console.log('1. Run the database schema update: npx tsx scripts/add-dual-mode-schema.sql');
  console.log('2. Start your development server: npm run dev');
  console.log('3. Test the chat functionality at /chat');
}

// Run the fix
fixImports();