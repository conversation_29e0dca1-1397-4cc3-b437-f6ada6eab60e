
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from '../shared/schema';
import logger from './utils/logger';

const connectionString = process.env.DATABASE_URL || 'postgresql://user:password@localhost:5432/rylie_db';

const client = postgres(connectionString, {
  max: 10,
  idle_timeout: 20,
  connect_timeout: 10,
});

// Create the drizzle instance - use default export
const db = drizzle(client, { schema });

export async function checkDatabaseHealth(): Promise<boolean> {
  try {
    await client`SELECT 1`;
    logger.info('Database connection is healthy');
    return true;
  } catch (error) {
    const err = error instanceof Error ? error : new Error(String(error));
    logger.error('Database health check failed', { error: err.message });
    return false;
  }
}

export async function closeDatabaseConnection(): Promise<void> {
  try {
    await client.end();
    logger.info('Database connection closed successfully');
  } catch (error) {
    const err = error instanceof Error ? error : new Error(String(error));
    logger.error('Error closing database connection', { error: err.message });
  }
}

export { client };

export async function executeQuery<T>(queryFn: () => Promise<T>): Promise<T> {
  try {
    return await queryFn();
  } catch (error) {
    const err = error instanceof Error ? error : new Error(String(error));
    logger.error('Database query failed', { error: err.message });
    throw err;
  }
}

// Default export for the database instance
export default db;
