
import nodemailer from 'nodemailer';
import logger from '../utils/logger';

// Email configuration
interface EmailConfig {
  service?: string;
  host?: string;
  port?: number;
  secure?: boolean;
  auth: {
    user: string;  
    pass: string;
  };
  pool?: boolean;
  maxConnections?: number;
  maxMessages?: number;
}

interface EmailOptions {
  to: string | string[];
  subject: string;
  html?: string;  
  text?: string;
  from?: string;
  replyTo?: string;
  attachments?: Array<{
    filename: string;
    content: Buffer | string;
    contentType?: string;
  }>;
}

interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  exponentialBackoff: boolean;
}

class EmailService {
  private transporter: nodemailer.Transporter | null = null;
  private config: EmailConfig;
  private retryConfig: RetryConfig;
  private isInitialized = false;

  constructor() {
    this.config = this.getEmailConfig();
    this.retryConfig = {
      maxRetries: parseInt(process.env.EMAIL_MAX_RETRIES || '3'),
      baseDelay: parseInt(process.env.EMAIL_RETRY_DELAY || '1000'),
      maxDelay: parseInt(process.env.EMAIL_MAX_DELAY || '30000'),
      exponentialBackoff: true
    };
  }

  private getEmailConfig(): EmailConfig {
    const emailService = process.env.EMAIL_SERVICE;
    
    switch (emailService) {
      case 'sendgrid':
        return {
          host: 'smtp.sendgrid.net',
          port: 587,
          secure: false,
          auth: {
            user: 'apikey',
            pass: process.env.SENDGRID_API_KEY || ''
          },
          pool: true,
          maxConnections: 5,
          maxMessages: 100
        };
        
      case 'gmail':
        return {
          service: 'gmail',
          auth: {
            user: process.env.GMAIL_USER || '',
            pass: process.env.GMAIL_APP_PASSWORD || ''
          },
          pool: true,
          maxConnections: 3,
          maxMessages: 50
        };
        
      case 'smtp':
      default:
        return {
          host: process.env.SMTP_HOST || '0.0.0.0',
          port: parseInt(process.env.SMTP_PORT || '587'),
          secure: process.env.SMTP_SECURE === 'true',
          auth: {
            user: process.env.SMTP_USER || '',
            pass: process.env.SMTP_PASSWORD || ''
          },
          pool: true,
          maxConnections: 5,
          maxMessages: 100
        };
    }
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      if (!this.config.auth.user || !this.config.auth.pass) {
        throw new Error('Email authentication credentials not configured');
      }

      this.transporter = nodemailer.createTransport(this.config);
      await this.testConnection();
      
      this.isInitialized = true;
      logger.info('Email service initialized', {
        service: process.env.EMAIL_SERVICE || 'smtp'
      });

    } catch (error) {
      logger.error('Failed to initialize email service:', error);
      
      if (process.env.NODE_ENV === 'development') {
        logger.warn('Using test email transporter for development');
        this.transporter = nodemailer.createTransport({
          host: '0.0.0.0',
          port: 1025,
          ignoreTLS: true
        });
        this.isInitialized = true;
      } else {
        throw error;
      }
    }
  }

  private async testConnection(): Promise<void> {
    if (!this.transporter) {
      throw new Error('Email transporter not initialized');
    }

    try {
      await this.transporter.verify();
      logger.debug('Email connection test successful');
    } catch (error) {
      logger.error('Email connection test failed:', error);
      throw new Error(`Email service connection failed: ${error.message}`);
    }
  }

  private calculateDelay(attempt: number): number {
    if (!this.retryConfig.exponentialBackoff) {
      return this.retryConfig.baseDelay;
    }

    const exponentialDelay = this.retryConfig.baseDelay * Math.pow(2, attempt - 1);
    const jitterDelay = exponentialDelay + Math.random() * 1000;
    return Math.min(jitterDelay, this.retryConfig.maxDelay);
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async sendEmail(options: EmailOptions): Promise<{ success: boolean; messageId?: string; error?: string }> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!this.transporter) {
      return { success: false, error: 'Email service not available' };
    }

    if (!options.to || !options.subject) {
      return { success: false, error: 'Missing required email fields' };
    }

    if (!options.html && !options.text) {
      return { success: false, error: 'Email must have content' };
    }

    const emailData = {
      from: options.from || process.env.EMAIL_FROM || '<EMAIL>',
      to: Array.isArray(options.to) ? options.to.join(', ') : options.to,
      subject: options.subject,
      html: options.html,
      text: options.text,
      replyTo: options.replyTo,
      attachments: options.attachments
    };

    for (let attempt = 1; attempt <= this.retryConfig.maxRetries; attempt++) {
      try {
        logger.debug(`Sending email attempt ${attempt}/${this.retryConfig.maxRetries}`);
        const result = await this.transporter.sendMail(emailData);
        
        logger.info('Email sent successfully', {
          messageId: result.messageId,
          to: emailData.to,
          subject: emailData.subject
        });

        return { success: true, messageId: result.messageId };

      } catch (error: any) {
        logger.warn(`Email send attempt ${attempt} failed:`, {
          error: error.message,
          code: error.code
        });

        const nonRetryableErrors = ['EAUTH', 'EENVELOPE', 'EMESSAGE'];
        if (nonRetryableErrors.includes(error.code) || attempt === this.retryConfig.maxRetries) {
          logger.error('Email send failed permanently:', error);
          return { success: false, error: error.message };
        }

        const delayMs = this.calculateDelay(attempt);
        await this.delay(delayMs);
      }
    }

    return { success: false, error: 'Failed to send email after all retries' };
  }

  async sendTemplatedEmail(
    templateName: string,
    to: string | string[],
    variables: Record<string, any>
  ): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      const template = await this.getEmailTemplate(templateName);
      if (!template) {
        return { success: false, error: `Template '${templateName}' not found` };
      }

      const subject = this.replaceVariables(template.subject, variables);
      const html = this.replaceVariables(template.html, variables);
      const text = template.text ? this.replaceVariables(template.text, variables) : undefined;

      return await this.sendEmail({ to, subject, html, text });
    } catch (error: any) {
      logger.error('Failed to send templated email:', error);
      return { success: false, error: error.message };
    }
  }

  private replaceVariables(content: string, variables: Record<string, any>): string {
    return content.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return variables[key] || match;
    });
  }

  private async getEmailTemplate(templateName: string): Promise<{
    subject: string;
    html: string;
    text?: string;
  } | null> {
    const templates: Record<string, any> = {
      'welcome': {
        subject: 'Welcome to {{companyName}}',
        html: `
          <h1>Welcome {{userName}}!</h1>
          <p>Thank you for joining {{companyName}}.</p>
          <p>Your account has been created successfully.</p>
        `,
        text: 'Welcome {{userName}}! Thank you for joining {{companyName}}.'
      },
      'password_reset': {
        subject: 'Reset your password',
        html: `
          <h1>Password Reset Request</h1>
          <p>You requested a password reset for your {{companyName}} account.</p>
          <p><a href="{{resetLink}}">Click here to reset your password</a></p>
          <p>This link will expire in 1 hour.</p>
        `,
        text: 'Reset your password: {{resetLink}} (expires in 1 hour)'
      }
    };

    return templates[templateName] || null;
  }

  async getServiceStatus(): Promise<{
    isInitialized: boolean;
    isConnected: boolean;
    error?: string;
  }> {
    if (!this.isInitialized) {
      return {
        isInitialized: false,
        isConnected: false,
        error: 'Email service not initialized'
      };
    }

    try {
      await this.testConnection();
      return { isInitialized: true, isConnected: true };
    } catch (error: any) {
      return {
        isInitialized: true,
        isConnected: false,
        error: error.message
      };
    }
  }

  async close(): Promise<void> {
    if (this.transporter && this.isInitialized) {
      try {
        this.transporter.close();
        logger.info('Email service connection closed');
      } catch (error) {
        logger.error('Error closing email service:', error);
      }
    }
    this.isInitialized = false;
  }
}

export const emailService = new EmailService();

export async function sendEmail(options: EmailOptions) {
  return emailService.sendEmail(options);
}

export async function sendTemplatedEmail(
  templateName: string,
  to: string | string[],
  variables: Record<string, any>
) {
  return emailService.sendTemplatedEmail(templateName, to, variables);
}

export async function initializeEmailService() {
  return emailService.initialize();
}

export async function getEmailServiceStatus() {
  return emailService.getServiceStatus();
}
