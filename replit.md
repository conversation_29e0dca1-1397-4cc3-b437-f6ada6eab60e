# Rylie AI - Automotive Dealership Conversational AI Platform

## Overview

Rylie AI is a conversational AI platform designed for automotive dealerships. It enables dealerships to manage customer interactions through AI-powered messaging, with features like inventory awareness, persona customization, and intelligent escalation to human support when needed. The application follows a modern web stack architecture with a React frontend and Node.js backend, using Drizzle ORM for database interaction.

## User Preferences

Preferred communication style: Simple, everyday language.

## System Architecture

The application follows a full-stack JavaScript/TypeScript architecture with the following major components:

1. **Frontend**: React-based SPA with TypeScript, using <PERSON><PERSON><PERSON> for routing and TanStack Query for data fetching
2. **Backend**: Express.js server in TypeScript
3. **Database**: Postgres with Drizzle ORM
4. **API Layer**: RESTful API endpoints with validation using Zod
5. **Authentication**: Session-based and API key authentication
6. **AI Integration**: OpenAI API for conversational capabilities

The application is structured as a monorepo with clear separation between client, server, and shared code.

### Directory Structure

- `/client`: React frontend application
- `/server`: Express.js backend server
- `/shared`: Shared code between client and server (schema definitions, types)
- `/migrations`: Database migration files generated by Drizzle

## Key Components

### Frontend Components

1. **Pages**: Dashboard, Conversations, Inventory, Personas, Analytics, and Settings
2. **UI Components**: Using Shadcn UI library with TailwindCSS for styling
3. **State Management**: React hooks and TanStack Query for server state
4. **Data Visualization**: Charts for analytics using Recharts

### Backend Components

1. **API Routes**: RESTful endpoints for dealership, vehicle, conversation, and persona management
2. **Authentication**: Middleware for API key validation and session management
3. **Database Access**: Storage layer for database operations
4. **AI Service**: Integration with OpenAI for generating responses and analyzing customer intent

### Database Schema

The database schema includes the following main entities:

1. **Users**: Dealership staff accounts
2. **Dealerships**: Automotive dealership information
3. **Vehicles**: Inventory of vehicles at dealerships
4. **Conversations**: Customer interactions
5. **Messages**: Individual messages within conversations
6. **Personas**: AI personality configurations for different dealerships
7. **API Keys**: Authentication tokens for API access

## Data Flow

1. **Customer Messaging Flow**:
   - Customer sends a message through SMS or other channels
   - Message is received by the API, validated, and stored
   - OpenAI service analyzes the message for intent and generates a response
   - Response is sent back to the customer
   - If escalation is needed, conversation is flagged for human attention

2. **Dashboard Flow**:
   - Dealership staff access the dashboard
   - Stats and recent conversations are loaded
   - Staff can view, respond to, or take over AI conversations

3. **Inventory Integration**:
   - Vehicle inventory is maintained in the database
   - AI responses include relevant vehicle information when appropriate
   - Inventory changes are reflected in conversations in real-time

## External Dependencies

### Frontend Dependencies
- React and React DOM for UI
- TanStack Query for data fetching
- Wouter for routing
- Shadcn UI components (based on Radix UI)
- TailwindCSS for styling
- Recharts for data visualization

### Backend Dependencies
- Express.js for API server
- OpenAI SDK for AI capabilities
- Drizzle ORM for database access
- Zod for validation
- Passport.js for authentication
- Session management with express-session

### Database
- PostgreSQL (via Neon serverless Postgres)
- Drizzle ORM for type-safe database access

## Deployment Strategy

The application is configured for deployment on Replit with the following setup:

1. **Development Mode**:
   - Uses `npm run dev` to start both frontend and backend in development mode
   - Vite serves the frontend with hot module replacement
   - Backend runs with tsx for TypeScript execution

2. **Production Build**:
   - Frontend: Built with Vite (`npm run build`)
   - Backend: Bundled with esbuild
   - Static assets served by Express.js

3. **Database**:
   - Uses Neon serverless PostgreSQL
   - Connection established via environment variable `DATABASE_URL`
   - Schema migrations managed with Drizzle Kit

4. **Environment Variables**:
   - DATABASE_URL: PostgreSQL connection string
   - OPENAI_API_KEY: For AI capabilities
   - SESSION_SECRET: For secure session management
   - NODE_ENV: To distinguish between development and production

## Getting Started

To begin development:

1. Ensure PostgreSQL database is provisioned
2. Set required environment variables
3. Run `npm run dev` to start the development server
4. Access the application at http://localhost:5000

For database migrations:
- Run `npm run db:push` to apply schema changes

## API Endpoints

The application exposes several RESTful endpoints:

1. **Authentication**:
   - `/api/auth/login`: User login
   - `/api/auth/logout`: User logout

2. **Conversations**:
   - `/api/conversations`: Manage customer conversations
   - `/api/messages`: Handle individual messages

3. **Inventory**:
   - `/api/vehicles`: Vehicle inventory management
   - `/api/dealerships`: Dealership information

4. **Personas**:
   - `/api/personas`: AI personality configuration