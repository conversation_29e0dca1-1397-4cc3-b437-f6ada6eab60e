modules = ["nodejs-20", "web", "postgresql-16"]
run = "npm run dev"
hidden = [".config", ".git", "generated-icon.png", "node_modules", "dist"]

[nix]
channel = "stable-24_05"

[deployment]
deploymentTarget = "autoscale"
build = ["sh", "-c", "npm run build"]
run = ["sh", "-c", "NODE_ENV=production SKIP_REDIS=true npm run start"]

[[ports]]
localPort = 3000
externalPort = 3000

[[ports]]
localPort = 3001
externalPort = 3001

[[ports]]
localPort = 5000
externalPort = 80

[[ports]]
localPort = 24678
externalPort = 3002

[workflows]
runButton = "Start fixed application"

[[workflows.workflow]]
name = "Project"
mode = "parallel"
author = "agent"

[[workflows.workflow.tasks]]
task = "workflow.run"
args = "Start application"

[[workflows.workflow]]
name = "Start application"
author = "agent"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "npm run dev"
waitForPort = 5000

[[workflows.workflow]]
name = "Start fixed application"
author = 42522474
mode = "sequential"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "npm run dev"
