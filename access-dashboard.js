import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';
import { Pool } from 'pg';

// Get directory name in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create Express app
const app = express();
const port = 3000;

// Database connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URL
});

// Middleware
app.use(express.json());
app.use(express.static('.'));

// Create a super admin user in memory
const mockUser = {
  id: 1,
  username: 'superadmin',
  name: 'Super Admin',
  email: '<EMAIL>',
  role: 'super_admin',
  dealership_id: null
};

// Auth bypass routes
app.get('/api/user', (req, res) => {
  // Always return the mock super admin user
  res.json(mockUser);
});

app.post('/api/login', (req, res) => {
  // Always return successful login
  res.json({
    success: true,
    user: mockUser
  });
});

// Real database access endpoints
app.get('/api/dealerships', async (req, res) => {
  try {
    const result = await pool.query('SELECT * FROM dealerships');
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching dealerships:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

app.get('/api/users', async (req, res) => {
  try {
    const result = await pool.query('SELECT id, username, email, name, role, dealership_id FROM users');
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Create simple HTML page with links to all dashboard pages
app.get('/', (req, res) => {
  const html = `
  <!DOCTYPE html>
  <html>
  <head>
    <title>Direct Dashboard Access</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        line-height: 1.6;
      }
      h1 {
        color: #4f46e5;
        border-bottom: 1px solid #e5e7eb;
        padding-bottom: 10px;
      }
      ul {
        list-style-type: none;
        padding: 0;
      }
      li {
        margin: 10px 0;
      }
      a {
        color: #4f46e5;
        text-decoration: none;
        padding: 8px 16px;
        background-color: #f3f4f6;
        border-radius: 4px;
        display: inline-block;
        transition: background-color 0.2s;
      }
      a:hover {
        background-color: #e5e7eb;
      }
      .section {
        margin-bottom: 30px;
        padding: 15px;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
      }
    </style>
  </head>
  <body>
    <h1>RylieAI Dashboard Direct Access</h1>
    <p>Click on any link below to access the dashboard pages directly (no login required):</p>
    
    <div class="section">
      <h2>Main Pages</h2>
      <ul>
        <li><a href="/dashboard">Dashboard</a></li>
        <li><a href="/system">System Settings</a></li>
        <li><a href="/prompt-library">Prompt Library</a></li>
        <li><a href="/personas">Personas</a></li>
        <li><a href="/enhanced-prompt-testing">Enhanced Prompt Testing</a></li>
        <li><a href="/simple-prompt-testing">Simple Prompt Testing</a></li>
      </ul>
    </div>
    
    <div class="section">
      <h2>Administration</h2>
      <ul>
        <li><a href="/settings">Settings</a></li>
        <li><a href="/users">User Management</a></li>
        <li><a href="/dealerships">Dealership Management</a></li>
        <li><a href="/analytics">Analytics</a></li>
      </ul>
    </div>
    
    <div class="section">
      <h2>Database Status</h2>
      <div id="db-status">Loading database status...</div>
    </div>
  </body>
  <script>
    // Fetch database status
    fetch('/api/db-status')
      .then(response => response.json())
      .then(data => {
        const statusDiv = document.getElementById('db-status');
        let html = '<ul>';
        for (const [table, count] of Object.entries(data)) {
          html += \`<li>\${table}: \${count} records</li>\`;
        }
        html += '</ul>';
        statusDiv.innerHTML = html;
      })
      .catch(error => {
        document.getElementById('db-status').innerHTML = 
          '<p style="color: red">Error connecting to database: ' + error.message + '</p>';
      });
  </script>
  </html>
  `;
  
  res.send(html);
});

// Database status endpoint
app.get('/api/db-status', async (req, res) => {
  try {
    const tables = ['users', 'dealerships', 'personas', 'vehicles', 'sessions', 'api_keys'];
    const results = {};
    
    for (const table of tables) {
      try {
        const result = await pool.query(`SELECT COUNT(*) FROM ${table}`);
        results[table] = parseInt(result.rows[0].count);
      } catch (error) {
        results[table] = `Error: ${error.message}`;
      }
    }
    
    res.json(results);
  } catch (error) {
    res.status(500).json({ error: 'Database error' });
  }
});

// Handle all routes to enable client-side routing
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'direct-dashboard.html'));
});

// Start the server
app.listen(port, '0.0.0.0', async () => {
  console.log(`Direct dashboard access server running at http://localhost:${port}`);
  
  // Test database connection
  try {
    const result = await pool.query('SELECT NOW()');
    console.log(`Database connected: ${result.rows[0].now}`);
  } catch (error) {
    console.error('Database connection error:', error.message);
  }
});