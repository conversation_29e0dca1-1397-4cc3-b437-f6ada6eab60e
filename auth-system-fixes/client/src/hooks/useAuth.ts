import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";

export interface User {
  id: number;
  username: string;
  email: string | null;
  name: string | null;
  role: string;
  dealership_id: number | null;
}

export interface LoginData {
  username?: string;
  email?: string;
  password: string;
}

export interface RegisterData {
  username: string;
  password: string;
  email?: string;
  firstName?: string;
  lastName?: string;
}

// API functions
async function fetchCurrentUser(): Promise<User | null> {
  try {
    console.log('Fetching current user...');
    const response = await fetch('/api/user', {
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log('User response status:', response.status);

    if (response.status === 401) {
      console.log('Not authenticated (401)');
      return null; // Not authenticated
    }

    if (!response.ok) {
      console.log('Response not OK:', response.status);
      return null;
    }

    const userData = await response.json();
    console.log('User data received:', userData);
    return userData;
  } catch (error) {
    console.error('Error fetching user:', error);
    return null;
  }
}

async function loginUser(credentials: LoginData): Promise<User> {
  const identifier = credentials.username || credentials.email;
  console.log('Attempting login with identifier:', identifier);

  try {
    const response = await fetch('/api/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify(credentials),
    });

    console.log('Login response status:', response.status);

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Login failed:', errorData);
      throw new Error(errorData.error || 'Login failed');
    }

    const userData = await response.json();
    console.log('Login successful, user data:', userData);
    return userData;
  } catch (error) {
    console.error('Login error:', error);
    throw error;
  }
}

async function registerUser(credentials: RegisterData): Promise<User> {
  const response = await fetch('/api/register', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
    body: JSON.stringify(credentials),
  });

  if (!response.ok) {
    const error = await response.text();
    throw new Error(error || 'Registration failed');
  }

  return response.json();
}

async function logoutUser(): Promise<void> {
  const response = await fetch('/api/logout', {
    method: 'POST',
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error('Logout failed');
  }
}

export function useAuth() {
  const queryClient = useQueryClient();

  // Get current user
  const {
    data: user,
    error,
    isLoading,
    refetch
  } = useQuery({
    queryKey: ["/api/user"],
    queryFn: fetchCurrentUser,
    retry: false,
    refetchOnWindowFocus: false,
  });

  // Login mutation
  const loginMutation = useMutation({
    mutationFn: loginUser,
    onSuccess: (userData) => {
      // Update cache with user data
      queryClient.setQueryData(["/api/user"], userData);
    }
  });

  // Register mutation
  const registerMutation = useMutation({
    mutationFn: registerUser,
    onSuccess: (userData) => {
      // Update cache with user data
      queryClient.setQueryData(["/api/user"], userData);
    }
  });

  // Logout mutation
  const logoutMutation = useMutation({
    mutationFn: logoutUser,
    onSuccess: () => {
      // Clear user from cache
      queryClient.setQueryData(["/api/user"], null);
    }
  });

  // Simple logout function that uses the mutation
  const logout = () => {
    logoutMutation.mutate();
  };

  return {
    user: user ?? null,
    isLoading,
    isAuthenticated: !!user,
    error,
    loginMutation,
    logoutMutation,
    registerMutation,
    refetchUser: refetch,
    logout // Add the simple logout function
  };
}